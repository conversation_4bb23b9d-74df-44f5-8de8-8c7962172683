<?php
// Script para probar la corrección en producción
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== PRUEBA DE CORRECCIÓN EN PRODUCCIÓN ===\n";

// Simular datos POST como los que enviaría el formulario
$_POST = [
    'rut_cliente' => '12345678-9',
    'razon_social' => 'EMPRESA PRUEBA PRODUCCION LTDA',
    'nombre_ejecutivo' => 'Ejecutivo Test',
    'telefono_ejecutivo' => '*********',
    'email_ejecutivo' => '<EMAIL>',
    'direccion_comercial' => 'Dirección Test 123',
    'tipo_persona' => 'Natural',
    'direccion_atencion' => 'Dirección Atención 456'
];

echo "Datos de prueba configurados:\n";
echo "RUT: " . $_POST['rut_cliente'] . "\n";
echo "Razón Social: " . $_POST['razon_social'] . "\n\n";

echo "Verificando sintaxis del archivo guardar_prospecto_inteletgroup.php...\n";

// Verificar sintaxis del archivo
$output = [];
$return_var = 0;
exec('php -l guardar_prospecto_inteletgroup.php 2>&1', $output, $return_var);

if ($return_var === 0) {
    echo "✅ Sintaxis correcta\n";
} else {
    echo "❌ Error de sintaxis:\n";
    foreach ($output as $line) {
        echo "   $line\n";
    }
    exit(1);
}

echo "\nProbando inclusión del archivo...\n";

// Capturar la salida
ob_start();
try {
    include 'guardar_prospecto_inteletgroup.php';
    $output = ob_get_clean();
    echo "✅ Archivo incluido sin errores fatales\n";
    echo "Salida capturada:\n";
    echo $output . "\n";
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Exception: " . $e->getMessage() . "\n";
    echo "Archivo: " . $e->getFile() . "\n";
    echo "Línea: " . $e->getLine() . "\n";
} catch (Error $e) {
    ob_end_clean();
    echo "❌ Fatal Error: " . $e->getMessage() . "\n";
    echo "Archivo: " . $e->getFile() . "\n";
    echo "Línea: " . $e->getLine() . "\n";
}

echo "\n=== FIN DE LA PRUEBA ===\n";
?>
