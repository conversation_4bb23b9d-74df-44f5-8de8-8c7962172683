<?php
// Archivo de debug para el login
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Debug del Sistema de Login</h2>";

try {
    // Incluir el archivo de configuración
    require_once 'init.php';
    echo "✅ init.php cargado correctamente<br>";
    
    // Probar conexión con con_db.php
    if (file_exists('con_db.php')) {
        echo "✅ con_db.php existe<br>";
        require_once 'con_db.php';
        echo "✅ con_db.php incluido correctamente<br>";
        
        // Verificar si la conexión existe
        if (isset($conexion)) {
            echo "✅ Variable \$conexion existe<br>";
            
            // Probar consulta específica del usuario administrador
            $rut = '12498646-k';
            $query = "SELECT id, nombre_usuario, correo, proyecto, password FROM tb_experian_usuarios WHERE rut = ?";
            $stmt = mysqli_prepare($conexion, $query);
            
            if ($stmt) {
                mysqli_stmt_bind_param($stmt, 's', $rut);
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);
                
                if ($result && mysqli_num_rows($result) > 0) {
                    $user = mysqli_fetch_assoc($result);
                    echo "✅ Usuario administrador encontrado:<br>";
                    echo "&nbsp;&nbsp;ID: " . $user['id'] . "<br>";
                    echo "&nbsp;&nbsp;Nombre: " . $user['nombre_usuario'] . "<br>";
                    echo "&nbsp;&nbsp;Correo: " . $user['correo'] . "<br>";
                    echo "&nbsp;&nbsp;Proyecto: " . $user['proyecto'] . "<br>";
                    echo "&nbsp;&nbsp;Password hash: " . substr($user['password'], 0, 20) . "...<br>";
                    
                    // Verificar password
                    $password = 'Cocl646k$';
                    if (password_verify($password, $user['password'])) {
                        echo "✅ Password verificado correctamente<br>";
                    } else {
                        echo "❌ Password no coincide<br>";
                        echo "Intentando verificación directa...<br>";
                        if ($password === $user['password']) {
                            echo "✅ Password coincide directamente (sin hash)<br>";
                        } else {
                            echo "❌ Password no coincide de ninguna forma<br>";
                        }
                    }
                } else {
                    echo "❌ Usuario administrador no encontrado en la base de datos<br>";
                }
                mysqli_stmt_close($stmt);
            } else {
                echo "❌ Error preparando consulta: " . mysqli_error($conexion) . "<br>";
            }
            
        } else {
            echo "❌ Variable \$conexion no existe<br>";
        }
    } else {
        echo "❌ con_db.php no existe<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br><pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h3>Información del Sistema</h3>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Entorno detectado: " . (defined('ENVIRONMENT') ? ENVIRONMENT : 'No definido') . "<br>";
echo "URL Base: " . (function_exists('base_url') ? base_url() : 'No disponible') . "<br>";

// Verificar extensiones necesarias
echo "<h3>Extensiones PHP</h3>";
echo "MySQLi: " . (extension_loaded('mysqli') ? '✅ Disponible' : '❌ No disponible') . "<br>";
echo "JSON: " . (extension_loaded('json') ? '✅ Disponible' : '❌ No disponible') . "<br>";
echo "Session: " . (extension_loaded('session') ? '✅ Disponible' : '❌ No disponible') . "<br>";

// Probar el ControllerGestar directamente
echo "<h3>Prueba de ControllerGestar</h3>";
if (file_exists('ControllerGestar.php')) {
    echo "✅ ControllerGestar.php existe<br>";
    echo "Tamaño del archivo: " . filesize('ControllerGestar.php') . " bytes<br>";
    
    // Verificar sintaxis del archivo
    $output = shell_exec("php -l ControllerGestar.php 2>&1");
    if (strpos($output, 'No syntax errors') !== false) {
        echo "✅ Sintaxis de ControllerGestar.php correcta<br>";
    } else {
        echo "❌ Error de sintaxis en ControllerGestar.php:<br>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
    }
} else {
    echo "❌ ControllerGestar.php no existe<br>";
}

echo "<h3>Archivos del Sistema</h3>";
$files = ['config.php', 'init.php', 'url_helpers.php', 'con_db.php', 'ControllerGestar.php'];
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ $file existe (" . filesize($file) . " bytes)<br>";
    } else {
        echo "❌ $file no existe<br>";
    }
}
?>
