<?php
// Script para probar el endpoint de producción usando cURL
echo "=== PRUEBA CURL PRODUCCIÓN ===\n";

// Datos de prueba con todos los campos requeridos según el código
$postData = [
    'usuario_id' => '4', // ID de usuario requerido
    'rut_cliente' => '99999999-9', // RUT que no debería existir
    'razon_social' => 'EMPRESA PRUEBA CURL LTDA',
    'nombre_ejecutivo' => 'Ejecutivo Curl',
    'rubro' => 'Servicios de prueba',
    'direccion_comercial' => 'Dirección Curl 123',
    'telefono_celular' => '987654321',
    'email' => '<EMAIL>',
    'tipo_persona' => 'Natural',
    'numero_pos' => '12345',
    'tipo_cuenta' => 'Corriente',
    'numero_cuenta_bancaria' => '123456789',
    'dias_atencion' => 'Lunes a Viernes',
    'horario_atencion' => '9:00 - 18:00',
    'contrata_boleta' => 'Boleta',
    'competencia_actual' => 'Ninguna'
];

echo "Datos a enviar:\n";
foreach ($postData as $key => $value) {
    echo "  $key: $value\n";
}
echo "\n";

// Configurar cURL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.gestarservicios.cl/intranet/dist/guardar_prospecto_inteletgroup.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

// Headers para simular un navegador
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded',
    'Accept: application/json, text/plain, */*',
    'X-Requested-With: XMLHttpRequest'
]);

echo "Enviando solicitud...\n";

// Ejecutar la solicitud
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

curl_close($ch);

echo "Código HTTP: $httpCode\n";

if ($error) {
    echo "Error cURL: $error\n";
} else {
    echo "Respuesta recibida:\n";
    echo "Longitud: " . strlen($response) . " caracteres\n";
    echo "Contenido:\n";
    echo $response . "\n";
    
    // Intentar decodificar JSON
    $jsonData = json_decode($response, true);
    if ($jsonData !== null) {
        echo "\nJSON decodificado:\n";
        print_r($jsonData);
    } else {
        echo "\nNo es JSON válido o está vacío\n";
        echo "Primeros 500 caracteres de la respuesta:\n";
        echo substr($response, 0, 500) . "\n";
    }
}

echo "\n=== FIN PRUEBA CURL ===\n";
?>
