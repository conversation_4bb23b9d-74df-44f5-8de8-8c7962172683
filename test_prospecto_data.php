<?php
require_once 'init.php';
require_once 'con_db.php';

// Configuración de errores para desarrollo
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>Test de Datos del Prospecto</h1>";

// Verificar conexión
if (!isset($mysqli)) {
    die("Error: Variable mysqli no está definida");
}

if ($mysqli->connect_error) {
    die("Error de conexión: " . $mysqli->connect_error);
}

echo "<p>Conexión a base de datos: <strong>OK</strong></p>";

// Obtener un prospecto de ejemplo
$prospecto_id = isset($_GET['prospecto_id']) ? intval($_GET['prospecto_id']) : 1;

echo "<h2>Datos del Prospecto ID: $prospecto_id</h2>";

// Consulta para obtener todos los campos
$stmt = $mysqli->prepare("
    SELECT 
        p.id, p.usuario_id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, 
        p.telefono_celular, p.nombre_ejecutivo, p.fecha_registro, p.direccion_comercial,
        p.numero_pos, p.tipo_cuenta, p.numero_cuenta_bancaria, p.dias_atencion,
        p.horario_atencion, p.contrata_boleta, p.competencia_actual, p.documentos_adjuntos,
        p.fecha_actualizacion, p.estado
    FROM tb_inteletgroup_prospectos p
    WHERE p.id = ?
    LIMIT 1
");

if (!$stmt) {
    die("Error preparando consulta: " . $mysqli->error);
}

$stmt->bind_param("i", $prospecto_id);
$stmt->execute();

$result = $stmt->get_result();
if ($result && $result->num_rows > 0) {
    $prospecto = $result->fetch_assoc();
    
    echo "<h3>Datos encontrados:</h3>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Campo</th><th>Valor</th></tr>";
    
    foreach ($prospecto as $campo => $valor) {
        $valor_mostrar = $valor === null ? '<em>NULL</em>' : htmlspecialchars($valor);
        echo "<tr><td><strong>$campo</strong></td><td>$valor_mostrar</td></tr>";
    }
    
    echo "</table>";
    
    // Verificar si hay campos vacíos o nulos
    echo "<h3>Análisis de campos:</h3>";
    $campos_vacios = [];
    $campos_con_datos = [];
    
    foreach ($prospecto as $campo => $valor) {
        if ($valor === null || $valor === '') {
            $campos_vacios[] = $campo;
        } else {
            $campos_con_datos[] = $campo;
        }
    }
    
    echo "<p><strong>Campos con datos (" . count($campos_con_datos) . "):</strong> " . implode(', ', $campos_con_datos) . "</p>";
    echo "<p><strong>Campos vacíos/nulos (" . count($campos_vacios) . "):</strong> " . implode(', ', $campos_vacios) . "</p>";
    
} else {
    echo "<p><strong>No se encontró el prospecto con ID: $prospecto_id</strong></p>";
    
    // Mostrar todos los prospectos disponibles
    $stmt2 = $mysqli->prepare("SELECT id, razon_social, rut_cliente FROM tb_inteletgroup_prospectos ORDER BY id LIMIT 10");
    $stmt2->execute();
    $result2 = $stmt2->get_result();
    
    if ($result2 && $result2->num_rows > 0) {
        echo "<h3>Prospectos disponibles:</h3>";
        echo "<ul>";
        while ($row = $result2->fetch_assoc()) {
            echo "<li><a href='?prospecto_id=" . $row['id'] . "'>ID: " . $row['id'] . " - " . htmlspecialchars($row['razon_social']) . " (RUT: " . htmlspecialchars($row['rut_cliente']) . ")</a></li>";
        }
        echo "</ul>";
    }
}

$stmt->close();

// Verificar estructura de la tabla
echo "<h2>Estructura de la tabla tb_inteletgroup_prospectos</h2>";
$result = $mysqli->query("DESCRIBE tb_inteletgroup_prospectos");
if ($result) {
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

$mysqli->close();
?>
