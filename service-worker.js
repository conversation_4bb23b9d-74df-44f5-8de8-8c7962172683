// ========================================================
// SERVICE WORKER - AUTO-DESACTIVACIÓN
// LIMPIA CACHÉS Y SE DESACTIVA PARA EVITAR BUCLES
// ========================================================

console.log('🔄 Service Worker: Iniciando proceso de auto-desactivación');

// Install Event - Limpiar cachés y preparar desactivación
self.addEventListener('install', function (event) {
    console.log('🚫 Service Worker: Install - Limpiando cachés antes de desactivar');
    event.waitUntil(
        caches.keys().then(function(cacheNames) {
            console.log('🗑️ Cachés encontrados:', cacheNames);
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    console.log('🗑️ Eliminando caché:', cacheName);
                    return caches.delete(cacheName);
                })
            );
        }).then(function() {
            console.log('✅ Cachés eliminados - Preparando desactivación');
            // NO hacer skipWaiting para evitar activación inmediata
        })
    );
});

// Activate Event - Desregistrar el service worker
self.addEventListener('activate', function (event) {
    console.log('🚫 Service Worker: Activate - DESREGISTRANDO para evitar bucles');
    event.waitUntil(
        Promise.all([
            // Limpiar cachés finales
            caches.keys().then(keys => {
                return Promise.all(keys.map(key => caches.delete(key)));
            }),
            // Desregistrar el service worker
            self.registration.unregister()
        ]).then(function() {
            console.log('✅ Service Worker desregistrado exitosamente');
            // Notificar a todas las páginas que se refresquen
            return self.clients.matchAll().then(clients => {
                clients.forEach(client => {
                    client.postMessage({type: 'SW_UNREGISTERED'});
                });
            });
        }).catch(function(error) {
            console.warn('⚠️ Error en desregistro:', error);
        })
    );
});

// NO INTERCEPTAR FETCH - Dejar que el navegador maneje todo normalmente
// El service worker se desactivará automáticamente

console.log('✅ Service Worker: Configuración completada - Se desactivará automáticamente');