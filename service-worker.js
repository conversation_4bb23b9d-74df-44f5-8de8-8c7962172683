// ========================================================
// SERVICE WORKER MODIFICADO - SIN CACHÉ TOTAL
// VERSIÓN ANTI-CACHÉ - NO GUARDAR NADA
// ========================================================

// NO USAR CACHÉ - Versión que elimina todo
const staticCacheName = 'no-cache-v' + Date.now();
const dynamicCacheName = 'no-runtime-cache-v' + Date.now();

// NO PRE-CACHEAR NADA
const precacheAssets = [];

// Install Event - ELIMINAR TODO EL CACHÉ
self.addEventListener('install', function (event) {
    console.log('🚫 Service Worker: ELIMINANDO TODO EL CACHÉ');
    event.waitUntil(
        caches.keys().then(function(cacheNames) {
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    console.log('🗑️ Eliminando caché:', cacheName);
                    return caches.delete(cacheName);
                })
            );
        }).then(function() {
            console.log('✅ Todos los cachés eliminados');
            // Forzar activación inmediata
            return self.skipWaiting();
        })
    );
});

// Activate Event - ELIMINAR TODO EL CACHÉ SIEMPRE
self.addEventListener('activate', function (event) {
    console.log('🚫 Service Worker: ACTIVANDO - ELIMINANDO TODO EL CACHÉ');
    event.waitUntil(
        caches.keys().then(keys => {
            console.log('🗑️ Cachés encontrados para eliminar:', keys);
            return Promise.all(keys.map(key => {
                console.log('🗑️ Eliminando caché:', key);
                return caches.delete(key);
            }));
        }).then(function() {
            console.log('✅ Todos los cachés eliminados en activate');
            // Tomar control inmediato de todas las páginas
            return self.clients.claim();
        })
    );
});

// Fetch Event - NO CACHEAR NADA, SIEMPRE FETCH DIRECTO
self.addEventListener('fetch', function (event) {
    console.log('🚫 Service Worker: Fetch SIN CACHÉ para:', event.request.url);

    // NO CACHEAR NADA - Siempre hacer fetch directo
    event.respondWith(
        fetch(event.request, {
            cache: 'no-store',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        }).then(response => {
            console.log('✅ Fetch directo exitoso para:', event.request.url);
            return response;
        }).catch(function(error) {
            console.error('❌ Error en fetch para:', event.request.url, error);
            // En caso de error, intentar fetch simple sin headers
            return fetch(event.request).catch(function() {
                // Último recurso: respuesta básica
                return new Response('Error de red', {
                    status: 503,
                    statusText: 'Service Unavailable'
                });
            });
        })
    );
});

// Evento adicional para limpiar cachés cuando se recibe un mensaje
self.addEventListener('message', function(event) {
    if (event.data && event.data.type === 'CLEAR_CACHE') {
        console.log('🚫 Service Worker: Recibido comando CLEAR_CACHE');
        event.waitUntil(
            caches.keys().then(function(cacheNames) {
                return Promise.all(
                    cacheNames.map(function(cacheName) {
                        console.log('🗑️ Eliminando caché por mensaje:', cacheName);
                        return caches.delete(cacheName);
                    })
                );
            }).then(function() {
                console.log('✅ Cachés eliminados por mensaje');
                // Notificar al cliente que se completó
                event.ports[0].postMessage({success: true});
            })
        );
    }
});

// Limpiar cachés cada 30 segundos
setInterval(function() {
    console.log('🚫 Service Worker: Limpieza periódica de cachés');
    caches.keys().then(function(cacheNames) {
        return Promise.all(
            cacheNames.map(function(cacheName) {
                console.log('🗑️ Eliminando caché periódico:', cacheName);
                return caches.delete(cacheName);
            })
        );
    }).then(function() {
        console.log('✅ Limpieza periódica completada');
    });
}, 30000); // 30 segundos