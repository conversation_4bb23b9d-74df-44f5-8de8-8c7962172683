<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Aumentar límites para evitar timeouts
ini_set('max_execution_time', 300);
ini_set('memory_limit', '256M');

// Log para debugging
error_log("=== INTELETGROUP_ADMIN_DASHBOARD.PHP INICIADO ===");

// Configuraciones de sesión ANTES de session_start() - Estrategia TQW
ini_set('session.use_cookies', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_lifetime', 86400); // 24 horas
ini_set('session.gc_maxlifetime', 86400);  // 24 horas

// Iniciar sesión si no está activa
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verificar si venimos de navegación hacia atrás
$referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
if (strpos($referer, $_SERVER['HTTP_HOST']) !== false) {
    // Regenerar ID de sesión sin destruir datos
    session_regenerate_id(false);
}

// Incluir utilidades de caché ANTES de cualquier salida
require_once 'cache_utils.php';

// LIMPIAR COMPLETAMENTE TODO EL CACHÉ
clear_all_cache();

// Aplicar headers ultra agresivos - SIN CACHÉ TOTAL
header('Content-Type: text/html; charset=UTF-8');
no_cache_headers(); // Headers ultra agresivos
dev_no_cache_headers(); // Headers adicionales

// Verificar autenticación y permisos de administrador
// Solo permitir acceso a usuarios de inteletGroup
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    // Si es Oriana (ID: 4), redirigir a su panel correspondiente
    if (isset($_SESSION['usuario_id']) && $_SESSION['usuario_id'] == 4) {
        header('Location: form_experian2.php');
        exit;
    }

    $errorMsg = urlencode("Usuario no autenticado o sin permisos para InteletGroup.");
    header('Location: login.php?error=' . $errorMsg);
    exit;
}

// Incluir archivos necesarios
require_once 'init.php';

try {
    require_once 'con_db.php';
} catch (Exception $e) {
    error_log("ERROR: Excepción al incluir con_db.php: " . $e->getMessage());
    die("Error de conexión a la base de datos: " . $e->getMessage());
}

// Verificar conexión a base de datos
if (!isset($mysqli)) {
    error_log("ERROR: Variable mysqli no está definida después de incluir con_db.php");
    die("Error de conexión a la base de datos. Variable mysqli no definida.");
}

if ($mysqli->connect_error) {
    error_log("ERROR: Error de conexión MySQL: " . $mysqli->connect_error);
    die("Error de conexión a la base de datos: " . $mysqli->connect_error);
}

error_log("DEBUG: Conexión a BD establecida correctamente");

// Verificar si es administrador (aquí puedes ajustar la lógica según tu sistema de roles)
$es_admin = true; // Por ahora lo dejamos en true, pero deberías verificar el rol del usuario

// Obtener información del usuario logueado
$usuario_id = $_SESSION['usuario_id'];
$usuario_logueado_nombre = $_SESSION['nombre_usuario'] ?? 'Usuario';  // Variable protegida para el usuario logueado
$proyecto = $_SESSION['proyecto'] ?? 'inteletGroup';

// Verificar información actual del usuario en la base de datos para asegurar datos correctos
try {
    $stmt = $mysqli->prepare("SELECT id, correo, nombre_usuario, proyecto, rol FROM tb_experian_usuarios WHERE id = ?");
    if (!$stmt) {
        throw new Exception("Error preparando consulta: " . $mysqli->error);
    }

    $stmt->bind_param("i", $usuario_id);
    $stmt->execute();

    // Usar bind_result() en lugar de get_result() para compatibilidad con PHP 7.3
    $db_id = $db_correo = $db_nombre_usuario = $db_proyecto = $db_rol = null;
    $stmt->bind_result($db_id, $db_correo, $db_nombre_usuario, $db_proyecto, $db_rol);

    if ($stmt->fetch()) {
        // Usar información actualizada de la base de datos en lugar de la sesión
        $usuario_logueado_nombre = $db_nombre_usuario;  // Variable protegida
        $proyecto_db = $db_proyecto;
        $rol_db = $db_rol;

        // DEBUG: Log de información obtenida
        error_log("DEBUG DASHBOARD: Usuario ID=$usuario_id, Nombre BD='$db_nombre_usuario', Proyecto BD='$db_proyecto'");
        error_log("DEBUG DASHBOARD: Sesión nombre='" . ($_SESSION['nombre_usuario'] ?? 'N/A') . "', Sesión proyecto='" . ($_SESSION['proyecto'] ?? 'N/A') . "'");
    } else {
        error_log("ERROR: No se encontró usuario con ID " . $usuario_id . " en la base de datos");
    }
    $stmt->close();
} catch (Exception $e) {
    error_log("ERROR: Excepción en consulta de usuario: " . $e->getMessage());
    $usuario_logueado_nombre = $_SESSION['nombre_usuario'] ?? 'Usuario';
}

// Obtener parámetros de filtros
$filtro_ejecutivo = $_GET['ejecutivo'] ?? 'todos';
$filtro_periodo = $_GET['periodo'] ?? 'año';
$filtro_fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-01-01');
$filtro_fecha_fin = $_GET['fecha_fin'] ?? date('Y-12-31');

// Calcular fechas según el periodo seleccionado
switch($filtro_periodo) {
    case 'hoy':
        $filtro_fecha_inicio = date('Y-m-d');
        $filtro_fecha_fin = date('Y-m-d');
        break;
    case 'semana':
        $filtro_fecha_inicio = date('Y-m-d', strtotime('monday this week'));
        $filtro_fecha_fin = date('Y-m-d', strtotime('sunday this week'));
        break;
    case 'mes_actual':
        $filtro_fecha_inicio = date('Y-m-01');
        $filtro_fecha_fin = date('Y-m-t');
        break;
    case 'trimestre':
        $trimestre = ceil(date('n') / 3);
        $filtro_fecha_inicio = date('Y-') . sprintf('%02d', ($trimestre - 1) * 3 + 1) . '-01';
        $filtro_fecha_fin = date('Y-m-t', strtotime($filtro_fecha_inicio . ' +2 months'));
        break;
    case 'año':
        $filtro_fecha_inicio = date('Y-01-01');
        $filtro_fecha_fin = date('Y-12-31');
        break;
}

// Obtener lista de ejecutivos - SIMPLIFICADO
$ejecutivos = [];
error_log("DEBUG: Obteniendo lista de ejecutivos...");

// Inicializar estadísticas básicas
$stats = [
    'total_prospectos' => 0,
    'total_documentos' => 0,
    'prospectos_completos' => 0,
    'completitud_promedio' => 0,
    'prospectos_por_tipo' => [],
    'documentos_por_tipo' => [],
    'prospectos_por_ejecutivo' => [],
    'evolucion_mensual' => []
];

// Lista de prospectos simplificada
$prospectos_lista = [];

// Construir condición WHERE para filtros
$where_conditions = ["1=1"];
$params = [];
$types = "";

if ($filtro_ejecutivo !== 'todos') {
    $where_conditions[] = "p.usuario_id = ?";
    $params[] = $filtro_ejecutivo;
    $types .= "i";
}

$where_conditions[] = "DATE(p.fecha_registro) BETWEEN ? AND ?";
$params[] = $filtro_fecha_inicio;
$params[] = $filtro_fecha_fin;
$types .= "ss";

$where_clause = implode(" AND ", $where_conditions);

// Log de filtros aplicados
error_log("INFO: Filtros - Ejecutivo: $filtro_ejecutivo, Periodo: $filtro_periodo");
error_log("INFO: Fechas - Inicio: $filtro_fecha_inicio, Fin: $filtro_fecha_fin");
error_log("INFO: WHERE clause: $where_clause");

// Obtener estadísticas generales
$stats = [
    'total_prospectos' => 0,
    'total_documentos' => 0,
    'prospectos_completos' => 0,
    'completitud_promedio' => 0,
    'prospectos_por_tipo' => [],
    'documentos_por_tipo' => [],
    'prospectos_por_ejecutivo' => [],
    'evolucion_mensual' => []
];

// Total de prospectos
$query = "
    SELECT COUNT(*) as total_prospectos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE u.proyecto = 'inteletGroup'" . 
    ($filtro_ejecutivo !== 'todos' ? " AND p.usuario_id = ?" : "") . 
    ($filtro_fecha_inicio && $filtro_fecha_fin ? " AND DATE(p.fecha_registro) BETWEEN ? AND ?" : "") . "
";

error_log("DEBUG: Ejecutando consulta de prospectos: $query");

$stmt = $mysqli->prepare($query);
if ($stmt) {
    $prosp_params = [];
    $prosp_types = "";
    
    if ($filtro_ejecutivo !== 'todos') {
        $prosp_params[] = $filtro_ejecutivo;
        $prosp_types .= "i";
    }
    
    if ($filtro_fecha_inicio && $filtro_fecha_fin) {
        $prosp_params[] = $filtro_fecha_inicio;
        $prosp_params[] = $filtro_fecha_fin;
        $prosp_types .= "ss";
    }
    
    if (!empty($prosp_params)) {
        $stmt->bind_param($prosp_types, ...$prosp_params);
    }
    
    $stmt->execute();
    $stmt->bind_result($total_prospectos);
    if ($stmt->fetch()) {
        $stats['total_prospectos'] = $total_prospectos;
    }
    $stmt->close();
    error_log("INFO: Total prospectos obtenido: " . $stats['total_prospectos']);
} else {
    error_log("ERROR: Error en consulta de prospectos: " . $mysqli->error);
}

// Total de documentos
$query = "
    SELECT COUNT(DISTINCT d.id) as total_documentos 
    FROM tb_inteletgroup_documentos d
    INNER JOIN tb_inteletgroup_prospectos p ON d.prospecto_id = p.id
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE d.estado = 'Activo' AND u.proyecto = 'inteletGroup'" . 
    ($filtro_ejecutivo !== 'todos' ? " AND p.usuario_id = ?" : "") . 
    ($filtro_fecha_inicio && $filtro_fecha_fin ? " AND DATE(p.fecha_registro) BETWEEN ? AND ?" : "") . "
";

$stmt = $mysqli->prepare($query);
if ($stmt) {
    $doc_params = [];
    $doc_types = "";
    
    if ($filtro_ejecutivo !== 'todos') {
        $doc_params[] = $filtro_ejecutivo;
        $doc_types .= "i";
    }
    
    if ($filtro_fecha_inicio && $filtro_fecha_fin) {
        $doc_params[] = $filtro_fecha_inicio;
        $doc_params[] = $filtro_fecha_fin;
        $doc_types .= "ss";
    }
    
    if (!empty($doc_params)) {
        $stmt->bind_param($doc_types, ...$doc_params);
    }
    
    $stmt->execute();
    $stmt->bind_result($total_documentos);
    if ($stmt->fetch()) {
        $stats['total_documentos'] = $total_documentos;
    }
    $stmt->close();
    error_log("INFO: Total documentos obtenido: " . $stats['total_documentos']);
} else {
    error_log("ERROR: Error en consulta de documentos: " . $mysqli->error);
}

// Completitud promedio
$completitud_promedio = 0;
$query = "
    SELECT AVG(completitud) as promedio
    FROM (
        SELECT 
            p.id,
            (COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) / 
             NULLIF(COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END), 0) * 100) as completitud
        FROM tb_inteletgroup_prospectos p
        INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
        LEFT JOIN tb_inteletgroup_tipos_documento td ON
            (td.tipo_persona = p.tipo_persona COLLATE utf8mb4_spanish_ci 
             OR td.tipo_persona = 'Ambos' COLLATE utf8mb4_spanish_ci)
            AND td.estado = 'Activo' COLLATE utf8mb4_spanish_ci
        LEFT JOIN tb_inteletgroup_documentos d ON
            p.id = d.prospecto_id
            AND d.tipo_documento_id = td.id
            AND d.estado = 'Activo' COLLATE utf8mb4_spanish_ci
        WHERE u.proyecto = 'inteletGroup' COLLATE utf8mb4_spanish_ci" . 
        ($filtro_ejecutivo !== 'todos' ? " AND p.usuario_id = ?" : "") . 
        ($filtro_fecha_inicio && $filtro_fecha_fin ? " AND DATE(p.fecha_registro) BETWEEN ? AND ?" : "") . "
        GROUP BY p.id
    ) as t
    WHERE completitud IS NOT NULL
";

$stmt = $mysqli->prepare($query);
if ($stmt) {
    $compl_params = [];
    $compl_types = "";
    
    if ($filtro_ejecutivo !== 'todos') {
        $compl_params[] = $filtro_ejecutivo;
        $compl_types .= "i";
    }
    
    if ($filtro_fecha_inicio && $filtro_fecha_fin) {
        $compl_params[] = $filtro_fecha_inicio;
        $compl_params[] = $filtro_fecha_fin;
        $compl_types .= "ss";
    }
    
    if (!empty($compl_params)) {
        $stmt->bind_param($compl_types, ...$compl_params);
    }
    
    $stmt->execute();
    $stmt->bind_result($completitud_promedio);
    if ($stmt->fetch()) {
        $stats['completitud_promedio'] = round($completitud_promedio, 1);
    }
    $stmt->close();
    error_log("INFO: Completitud promedio: " . $stats['completitud_promedio']);
} else {
    error_log("ERROR: Error en consulta de completitud promedio: " . $mysqli->error);
    $stats['completitud_promedio'] = 0;
}

// Obtener número de prospectos con 100% de completitud (todos los documentos obligatorios)
$prospectos_completos = 0;
$query = "
    SELECT COUNT(*) as completos FROM (
        SELECT 
            p.id,
            COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as completados,
            COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
        FROM tb_inteletgroup_prospectos p
        INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
        LEFT JOIN tb_inteletgroup_tipos_documento td ON
            (td.tipo_persona = p.tipo_persona COLLATE utf8mb4_spanish_ci
             OR td.tipo_persona = 'Ambos' COLLATE utf8mb4_spanish_ci)
            AND td.estado = 'Activo' COLLATE utf8mb4_spanish_ci
        LEFT JOIN tb_inteletgroup_documentos d ON
            p.id = d.prospecto_id
            AND d.tipo_documento_id = td.id
            AND d.estado = 'Activo' COLLATE utf8mb4_spanish_ci
        WHERE u.proyecto = 'inteletGroup' COLLATE utf8mb4_spanish_ci" . 
        ($filtro_ejecutivo !== 'todos' ? " AND p.usuario_id = ?" : "") . 
        ($filtro_fecha_inicio && $filtro_fecha_fin ? " AND DATE(p.fecha_registro) BETWEEN ? AND ?" : "") . "
        GROUP BY p.id
        HAVING completados = total_obligatorios AND total_obligatorios > 0
    ) as t
";

$stmt = $mysqli->prepare($query);
if ($stmt) {
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $stmt->bind_result($prospectos_completos);
    if ($stmt->fetch()) {
        $stats['prospectos_completos'] = $prospectos_completos;
    }
    $stmt->close();
} else {
    error_log("ERROR: No se pudo preparar consulta de prospectos completos: " . $mysqli->error);
    $stats['prospectos_completos'] = 0;
}

// Prospectos por tipo de persona
error_log("DEBUG: Obteniendo prospectos por tipo de persona...");
$query = "
    SELECT 
        p.tipo_persona,
        COUNT(DISTINCT p.id) as total
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE u.proyecto = 'inteletGroup' " . (!empty($where_clause) ? " AND $where_clause" : "") . "
    GROUP BY p.tipo_persona
";

$stmt = $mysqli->prepare($query);
if ($stmt) {
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $stmt->bind_result($tipo_persona, $cantidad);
    while ($stmt->fetch()) {
        $stats['prospectos_por_tipo'][$tipo_persona] = $cantidad;
    }
    $stmt->close();
    error_log("DEBUG: Tipos encontrados: " . print_r($stats['prospectos_por_tipo'], true));
} else {
    error_log("ERROR: No se pudo preparar consulta por tipo: " . $mysqli->error);
}

// Prospectos por ejecutivo (top 10)
$query = "
    SELECT
        u.nombre_usuario,
        COUNT(DISTINCT p.id) as total_prospectos,
        COUNT(DISTINCT d.id) as total_documentos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    LEFT JOIN tb_inteletgroup_documentos d ON p.id = d.prospecto_id AND d.estado = 'Activo'
    WHERE u.proyecto = 'inteletGroup'" . 
    ($filtro_ejecutivo !== 'todos' ? " AND p.usuario_id = ?" : "") . 
    ($filtro_fecha_inicio && $filtro_fecha_fin ? " AND DATE(p.fecha_registro) BETWEEN ? AND ?" : "") . "
    GROUP BY u.id, u.nombre_usuario
    ORDER BY total_prospectos DESC
    LIMIT 10
";

$stmt = $mysqli->prepare($query);
if ($stmt) {
    $exec_params = [];
    $exec_types = "";
    
    if ($filtro_ejecutivo !== 'todos') {
        $exec_params[] = $filtro_ejecutivo;
        $exec_types .= "i";
    }
    
    if ($filtro_fecha_inicio && $filtro_fecha_fin) {
        $exec_params[] = $filtro_fecha_inicio;
        $exec_params[] = $filtro_fecha_fin;
        $exec_types .= "ss";
    }
    
    if (!empty($exec_params)) {
        $stmt->bind_param($exec_types, ...$exec_params);
    }
    
    $stmt->execute();
    $ejecutivo_nombre = $total_prosp = $total_docs = null;
    $stmt->bind_result($ejecutivo_nombre, $total_prosp, $total_docs);
    while ($stmt->fetch()) {
        $stats['prospectos_por_ejecutivo'][] = [
            'nombre' => $ejecutivo_nombre,
            'prospectos' => $total_prosp,
            'documentos' => $total_docs
        ];
    }
    $stmt->close();
}

// Registrar resultados
error_log("INFO: Prospectos por ejecutivo: " . count($stats['prospectos_por_ejecutivo']));

// Evolución diaria (últimos 30 días)
$query = "
    SELECT 
        DATE(p.fecha_registro) as dia,
        COUNT(DISTINCT p.id) as prospectos,
        COUNT(DISTINCT d.id) as documentos
    FROM tb_inteletgroup_prospectos p
    INNER JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    LEFT JOIN tb_inteletgroup_documentos d ON p.id = d.prospecto_id AND d.estado = 'Activo'
    WHERE p.fecha_registro >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    AND u.proyecto = 'inteletGroup'
    " . ($filtro_ejecutivo !== 'todos' ? "AND p.usuario_id = ?" : "") . "
    GROUP BY dia
    ORDER BY dia ASC
";

$stmt = $mysqli->prepare($query);
if ($stmt) {
    if ($filtro_ejecutivo !== 'todos') {
        $stmt->bind_param("i", $filtro_ejecutivo);
    }
    $stmt->execute();
    $stmt->bind_result($dia, $prospectos, $documentos);
    while ($stmt->fetch()) {
        $stats['evolucion_mensual'][] = [
            'dia' => $dia,
            'prospectos' => $prospectos,
            'documentos' => $documentos
        ];
    }
    $stmt->close();
    error_log("INFO: Datos de evolución obtenidos: " . count($stats['evolucion_mensual']));
}

// Obtener lista de todos los prospectos con detalles
$prospectos_lista = [];
$query = "
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE 1=1" .
    ($filtro_ejecutivo !== 'todos' ? " AND p.usuario_id = ?" : "") .
    ($filtro_fecha_inicio && $filtro_fecha_fin ? " AND DATE(p.fecha_registro) BETWEEN ? AND ?" : "") . "
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, p.telefono_celular, p.fecha_registro, u.nombre_usuario
    ORDER BY p.fecha_registro DESC
";

error_log("INFO: Ejecutando consulta de prospectos");
error_log("Query: " . $query);
error_log("Filtro ejecutivo: " . $filtro_ejecutivo);
error_log("Filtro fecha inicio: " . $filtro_fecha_inicio);
error_log("Filtro fecha fin: " . $filtro_fecha_fin);

// Modificamos la consulta para hacerla compatible con servidores MySQL sin mysqlnd
// Necesitamos usar bind_result en lugar de get_result

// Primero obtenemos los datos básicos
$query_basic = "
    SELECT p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
           p.email, p.telefono_celular, p.fecha_registro, u.nombre_usuario
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    WHERE 1=1" . 
    ($filtro_ejecutivo !== 'todos' ? " AND p.usuario_id = ?" : "") . 
    ($filtro_fecha_inicio && $filtro_fecha_fin ? " AND DATE(p.fecha_registro) BETWEEN ? AND ?" : "") . "
    ORDER BY p.fecha_registro DESC
";

$stmt = $mysqli->prepare($query_basic);
if ($stmt) {
    $lista_params = [];
    $lista_types = "";
    
    if ($filtro_ejecutivo !== 'todos') {
        $lista_params[] = $filtro_ejecutivo;
        $lista_types .= "i";
    }
    
    if ($filtro_fecha_inicio && $filtro_fecha_fin) {
        $lista_params[] = $filtro_fecha_inicio;
        $lista_params[] = $filtro_fecha_fin;
        $lista_types .= "ss";
    }
    
    if (!empty($lista_params)) {
        $stmt->bind_param($lista_types, ...$lista_params);
    }
    
    $stmt->execute();
    $stmt->bind_result($id, $tipo_persona, $rut_cliente, $razon_social, $rubro, $email, $telefono_celular, $fecha_registro, $ejecutivo_nombre_usuario);
    
    $prospectos_temp = [];
    while ($stmt->fetch()) {
        $prospectos_temp[$id] = [
            'id' => $id,
            'tipo_persona' => $tipo_persona,
            'rut_cliente' => $rut_cliente,
            'razon_social' => $razon_social,
            'rubro' => $rubro,
            'email' => $email,
            'telefono_celular' => $telefono_celular,
            'fecha_registro' => $fecha_registro,
            'ejecutivo_nombre_usuario' => $ejecutivo_nombre_usuario ?: 'Sin asignar',
            'ejecutivo_nombre_completo' => $ejecutivo_nombre_usuario ?: 'Sin asignar',
            'total_documentos' => 0,
            'obligatorios_completados' => 0,
            'total_obligatorios' => 0,
            'porcentaje_completado' => 0
        ];
    }
    $stmt->close();
    
    // Ahora obtenemos los datos de documentos por cada prospecto
    if (!empty($prospectos_temp)) {
        foreach ($prospectos_temp as $prospecto_id => $prospecto_data) {
            $query_docs = "
                SELECT
                    COUNT(DISTINCT d.id),
                    COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END),
                    COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END)
                FROM tb_inteletgroup_prospectos p
                LEFT JOIN tb_inteletgroup_tipos_documento td ON
                    (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci OR td.tipo_persona = 'Ambos')
                    AND td.estado = 'Activo'
                LEFT JOIN tb_inteletgroup_documentos d ON
                    p.id = d.prospecto_id
                    AND d.tipo_documento_id = td.id
                    AND d.estado = 'Activo'
                WHERE p.id = ?
            ";
            
            $stmt_docs = $mysqli->prepare($query_docs);
            if ($stmt_docs) {
                $stmt_docs->bind_param("i", $prospecto_id);
                $stmt_docs->execute();
                $stmt_docs->bind_result($total_docs, $obligatorios_comp, $total_oblig);
                
                if ($stmt_docs->fetch()) {
                    $porcentaje = $total_oblig > 0 ? round(($obligatorios_comp / $total_oblig) * 100) : 0;
                    
                    $prospectos_temp[$prospecto_id]['total_documentos'] = $total_docs;
                    $prospectos_temp[$prospecto_id]['obligatorios_completados'] = $obligatorios_comp;
                    $prospectos_temp[$prospecto_id]['total_obligatorios'] = $total_oblig;
                    $prospectos_temp[$prospecto_id]['porcentaje_completado'] = $porcentaje;
                }
                $stmt_docs->close();
            }
        }
        
        // Convertir de array asociativo a indexado
        $prospectos_lista = array_values($prospectos_temp);
    }
    // $stmt ya fue cerrado en la línea 577, no cerrar nuevamente
    error_log("INFO: Prospectos obtenidos: " . count($prospectos_lista));
} else {
    error_log("ERROR: Error en consulta de prospectos: " . $mysqli->error);
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel Administrativo - InteletGroup</title>

    <!-- ANTI-CACHÉ TOTAL - NO GUARDAR NADA -->
    <?php echo no_cache_meta(); ?>

    <!-- JavaScript para limpiar caché del navegador -->
    <?php echo browser_cache_clear_js(); ?>

    <?php
    // Generar un timestamp único para el versionado de recursos (anti-caché)
    $version = time() . '_' . mt_rand(10000, 99999) . '_xlsx';
    ?>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/v/bs5/jq-3.6.0/dt-1.11.5/datatables.min.css"/>
    <!-- Custom CSS con anti-caché -->
    <link rel="stylesheet" href="<?php echo version_url('css/dashboard.css'); ?>">
    <!-- Custom CSS con anti-caché -->
    <link rel="stylesheet" href="<?php echo version_url('css/inteletgroup_documentos_enhanced.css'); ?>">
    
    <style>
        /* Variables CSS de inteletgroup_documentos_enhanced */
        :root {
            --primary-blue: #2699FB;
            --primary-dark: #1e3a8a;
            --primary-medium: #3b82f6;
            --primary-light: #60a5fa;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --secondary-color: #6b7280;
            --light-gray: #f3f4f6;
            --border-radius: 12px;
            --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* Reduce global zoom to 80% for smaller UI */
        html {
            zoom: 70%; /* Works in most modern browsers */
        }
        
        /* Eliminar márgenes laterales y hacer que el contenedor ocupe todo el ancho */
        body {
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 100% !important;
            padding-left: 15px;
            padding-right: 15px;
            margin-left: 0;
            margin-right: 0;
            width: 100%;
        }
        
        .row {
            margin-left: 0;
            margin-right: 0;
        }
        
        [class*="col-"] {
            padding-left: 8px;
            padding-right: 8px;
        }

        /* Estilos adicionales para el dashboard */
        .dashboard-card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            transition: all 0.3s ease;
            background: white;
            padding: 1.5rem;
            height: 100%;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .kpi-card {
            text-align: center;
            padding: 1.2rem 0.8rem; /* Reducido de 2rem 1rem */
        }

        .kpi-value {
            font-size: 2.4rem; /* Reducido de 3rem */
            font-weight: 700;
            color: var(--primary-dark);
            line-height: 1;
            margin-bottom: 0.3rem; /* Reducido de 0.5rem */
        }

        .kpi-label {
            color: var(--secondary-color);
            font-size: 0.8rem; /* Reducido de 0.875rem */
            text-transform: uppercase;
            letter-spacing: 0.4px; /* Reducido de 0.5px */
            margin-bottom: 0.2rem; /* Añadido para mejor espaciado */
        }

        .kpi-change {
            font-size: 0.8rem; /* Reducido de 0.875rem */
            font-weight: 600;
            margin-top: 0.3rem; /* Reducido de 0.5rem */
        }

        .kpi-change.positive {
            color: var(--success-color);
        }

        .kpi-change.negative {
            color: var(--danger-color);
        }

        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }

        .filter-section {
            background: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
        }

        .table-container {
            background: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        /* Tabs personalizados */
        .nav-tabs {
            border-bottom: 2px solid #e5e7eb;
        }

        .nav-tabs .nav-link {
            color: var(--secondary-color);
            border: none;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-tabs .nav-link:hover {
            color: var(--primary-medium);
        }

        .nav-tabs .nav-link.active {
            color: var(--primary-dark);
            background: none;
            border: none;
            border-bottom: 3px solid var(--primary-medium);
        }

        /* Badges mejorados */
        .badge-natural {
            background-color: #3b82f6;
            color: white;
        }

        .badge-juridica {
            background-color: #10b981;
            color: white;
        }

        /* Responsive tables */
        .table-responsive {
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        /* Iconos en estadísticas */
        .stat-icon {
            width: 40px; /* Reducido de 50px */
            height: 40px; /* Reducido de 50px */
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.6rem; /* Reducido de 1rem */
            font-size: 1.2rem; /* Reducido de 1.5rem */
            color: white;
        }

        .stat-icon.blue {
            background: rgba(38, 153, 251, 0.1);
            color: var(--primary-blue);
        }

        .stat-icon.green {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .stat-icon.yellow {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .stat-icon.purple {
            background: rgba(139, 92, 246, 0.1);
            color: #8b5cf6;
        }
        
        .user-info-container .user-name {
            color: white !important;
            font-weight: 500;
            text-shadow: 0 0 2px rgba(0,0,0,0.3);
        }

        .header-action-btn {
            background-color: var(--primary-dark); /* Un azul oscuro de tu paleta */
            color: white !important; /* Texto blanco */
            border: 1px solid var(--primary-medium); /* Borde sutil */
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: background-color 0.3s ease, border-color 0.3s ease;
            display: inline-flex;
            align-items: center;
            text-decoration: none; /* Asegura que no tenga subrayado */
        }

        .header-action-btn:hover {
            background-color: var(--primary-blue); /* Un azul más claro al pasar el ratón */
            border-color: var(--primary-blue);
            color: white !important;
        }

        .header-action-btn .bi {
            margin-right: 0.5rem;
        }
        
        .page-header h1 {
            color: white !important;
        }

        /* Contenedor de tabla y panel */
        .table-panel-wrapper {
            display: flex;
            width: 100%;
            transition: all 0.3s ease;
            gap: 1rem;
            align-items: flex-start;
        }

        .table-container {
            width: 100%;
            transition: all 0.3s ease;
            flex-shrink: 1;
        }

        .table-container.sidebar-active {
            width: 50%;
        }

        /* Estilos para el panel lateral */
        .documents-sidebar {
            width: 0;
            background: white;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
            border-radius: var(--border-radius);
            opacity: 0;
        }

        .documents-sidebar.active {
            width: 50%;
            opacity: 1;
        }

        /* Overlay opcional - más sutil */
        .sidebar-overlay {
            display: none; /* Deshabilitado por defecto ya que no lo necesitamos */
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f8f9fa;
        }

        .sidebar-title {
            margin: 0;
            font-size: 1.25rem;
            color: var(--primary-dark);
        }

        .sidebar-body {
            flex: 1;
            overflow-y: auto;
            padding: 1.5rem;
        }

        .prospecto-info-panel {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding: 0;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e3e6f0;
            position: relative;
        }
        
        .info-panel-header {
            padding: 15px 20px;
            border-bottom: 2px solid #3498db;
            margin-bottom: 15px;
        }
        
        .info-panel-title {
            color: #2c3e50;
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
        }
        
        .info-panel-content {
            padding: 0 20px 20px 20px;
        }
        
        .info-column {
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            height: fit-content;
        }
        
        .row .col-4:nth-child(2) .info-column {
            border-left-color: #2ecc71;
        }
        
        .row .col-4:nth-child(3) .info-column {
            border-left-color: #9b59b6;
        }

        .info-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #34495e;
            width: 100px;
            flex-shrink: 0;
            margin-right: 10px;
        }

        .info-value {
            color: #2c3e50;
            background-color: rgba(52, 152, 219, 0.1);
            padding: 4px 10px;
            border-radius: 6px;
            font-weight: 500;
            word-break: break-word;
            flex: 1;
        }

        .documents-container {
            min-height: 200px;
        }

        .document-item {
            padding: 1rem;
            border: 1px solid #e9ecef;
            border-radius: 0.5rem;
            margin-bottom: 0.75rem;
            transition: all 0.2s ease;
        }

        .document-item:hover {
            border-color: var(--primary-light);
            background-color: #f8f9fa;
        }

        .document-item .bi {
            font-size: 1.2rem;
        }

        .document-item .bi-file-earmark-pdf {
            color: #dc3545;
        }

        .document-item .bi-file-earmark-word {
            color: #0066cc;
        }

        .document-item .bi-file-earmark-excel {
            color: #28a745;
        }

        .document-item .bi-file-earmark-image {
            color: #6610f2;
        }

        /* Responsive para el panel lateral */
        @media (max-width: 768px) {
            .documents-sidebar {
                width: 100%;
                right: -100%;
            }
        }

        /* Ajustes para los tabs del panel lateral - Exactamente como inteletgroup_documentos_enhanced.php */
        .documents-sidebar .nav-tabs {
            border-bottom: none;
            margin-bottom: 1.5rem;
        }

        .documents-sidebar .nav-tabs .nav-link {
            color: var(--secondary-color);
            border: none;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            margin-right: 0.5rem;
            background: transparent;
            position: relative;
        }

        .documents-sidebar .nav-tabs .nav-link:hover {
            color: var(--primary-medium);
            background: rgba(38, 153, 251, 0.05);
        }

        .documents-sidebar .nav-tabs .nav-link.active {
            color: var(--primary-dark);
            background: white;
            border: 1px solid #e5e7eb;
            border-bottom: none;
            box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
        }

        .documents-sidebar .nav-tabs .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: white;
        }

        /* Contenedor de documentos con el mismo estilo - 3 columnas por fila */
        .documents-sidebar .document-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.75rem;
            padding: 0 0.5rem;
        }
        
        /* Responsive para el grid de documentos */
        @media (max-width: 1200px) {
            .documents-sidebar .document-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .documents-sidebar .document-grid {
                grid-template-columns: 1fr;
            }
        }

        .documents-sidebar .document-card {
            border: 2px solid #e5e7eb;
            border-radius: var(--border-radius);
            padding: 1rem;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            background: white;
            min-height: 200px;
            display: flex;
            flex-direction: column;
        }
        
        /* Checkbox para selección de documentos */
        .documents-sidebar .document-checkbox {
            position: absolute;
            top: 0.5rem;
            left: 0.5rem;
            width: 18px;
            height: 18px;
            z-index: 10;
        }

        .documents-sidebar .document-card:hover {
            border-color: var(--primary-medium);
            transform: translateY(-4px);
            box-shadow: var(--box-shadow);
        }

        .documents-sidebar .document-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .documents-sidebar .document-card.pdf .document-icon { color: #dc2626; }
        .documents-sidebar .document-card.doc .document-icon { color: #2563eb; }
        .documents-sidebar .document-card.image .document-icon { color: #10b981; }
        .documents-sidebar .document-card.excel .document-icon { color: #059669; }

        .documents-sidebar .document-type-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--primary-dark);
            text-align: center;
            line-height: 1.3;
            margin-bottom: 0.75rem;
            min-height: 2.6rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .documents-sidebar .document-filename {
            font-size: 0.75rem;
            color: var(--secondary-color);
            text-align: center;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        /* Botones de selección múltiple */
        .documents-sidebar .selection-controls {
            margin-bottom: 1rem;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 0.5rem;
            border: 1px solid #e9ecef;
        }
        
        .documents-sidebar .btn-sm {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        /* Empty state dentro del sidebar */
        .documents-sidebar .empty-state {
            text-align: center;
            padding: 3rem 1rem;
        }

        .documents-sidebar .empty-state-icon {
            font-size: 3rem;
            color: #e5e7eb;
            margin-bottom: 1rem;
        }

        .documents-sidebar .empty-state-text {
            color: var(--secondary-color);
            font-size: 1rem;
        }

        /* Stats cards dentro del sidebar */
        .documents-sidebar .stats-card {
            text-align: center;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .documents-sidebar .stats-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-dark);
        }

        .documents-sidebar .stats-label {
            color: var(--secondary-color);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1>
                        <i class="bi bi-speedometer2 me-3"></i>
                        Panel Administrativo
                    </h1> 
                    <p class="mb-0 opacity-75">Vista global de prospectos y documentación - Actualizado</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="user-section d-flex justify-content-end align-items-center">
                        <a href="inteletgroup_documentos_enhanced.php" class="header-action-btn me-3">
                            <i class="bi bi-files me-2"></i>
                            Gestión Documentos
                        </a>
                        <a href="form_inteletgroup.php" class="header-action-btn me-3">
                            <i class="bi bi-plus-circle me-2"></i>
                            Nuevo Prospecto
                        </a>
                        <div class="user-info-container me-3">
                            <div class="user-name"><?php
                                // Mostrar solo el nombre del usuario
                                echo htmlspecialchars($usuario_logueado_nombre);
                            ?></div>
                        </div>
                        <a href="logout.php" class="logout-btn" title="Cerrar sesión">
                            <i class="bi bi-box-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Filtros -->
        <div class="filter-section">
            <form method="GET" id="filterForm">
                <div class="row g-3 align-items-end">
                    <div class="col-md-3">
                        <label class="form-label">Ejecutivo</label>
                        <select name="ejecutivo" class="form-select">
                            <option value="todos" <?php echo $filtro_ejecutivo == 'todos' ? 'selected' : ''; ?>>Todos los ejecutivos</option>
                            <?php foreach ($ejecutivos as $ejecutivo): ?>
                                <option value="<?php echo $ejecutivo['id']; ?>" 
                                        <?php echo $filtro_ejecutivo == $ejecutivo['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($ejecutivo['nombre_completo']); ?> 
                                    (<?php echo $ejecutivo['total_prospectos']; ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Periodo</label>
                        <select name="periodo" class="form-select">
                            <option value="hoy" <?php echo $filtro_periodo == 'hoy' ? 'selected' : ''; ?>>Hoy</option>
                            <option value="semana" <?php echo $filtro_periodo == 'semana' ? 'selected' : ''; ?>>Esta semana</option>
                            <option value="mes_actual" <?php echo $filtro_periodo == 'mes_actual' ? 'selected' : ''; ?>>Este mes</option>
                            <option value="trimestre" <?php echo $filtro_periodo == 'trimestre' ? 'selected' : ''; ?>>Este trimestre</option>
                            <option value="año" <?php echo $filtro_periodo == 'año' ? 'selected' : ''; ?>>Este año</option>
                            <option value="personalizado" <?php echo $filtro_periodo == 'personalizado' ? 'selected' : ''; ?>>Personalizado</option>
                        </select>
                    </div>
                    <div class="col-md-2" id="fecha_inicio_container" style="<?php echo $filtro_periodo == 'personalizado' ? '' : 'display:none;'; ?>">
                        <label class="form-label">Fecha inicio</label>
                        <input type="date" name="fecha_inicio" class="form-control" value="<?php echo $filtro_fecha_inicio; ?>">
                    </div>
                    <div class="col-md-2" id="fecha_fin_container" style="<?php echo $filtro_periodo == 'personalizado' ? '' : 'display:none;'; ?>">
                        <label class="form-label">Fecha fin</label>
                        <input type="date" name="fecha_fin" class="form-control" value="<?php echo $filtro_fecha_fin; ?>">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-funnel me-2"></i>
                            Aplicar filtros
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Primera fila: 4 KPIs -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="dashboard-card kpi-card h-100">
                    <div class="stat-icon blue mx-auto">
                        <i class="bi bi-people-fill"></i>
                    </div>
                    <div class="kpi-value"><?php echo number_format($stats['total_prospectos']); ?></div>
                    <div class="kpi-label">Total Prospectos</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="dashboard-card kpi-card h-100">
                    <div class="stat-icon green mx-auto">
                        <i class="bi bi-check-circle-fill"></i>
                    </div>
                    <div class="kpi-value"><?php echo number_format($stats['prospectos_completos']); ?></div>
                    <div class="kpi-label">Prospectos Completos</div>
                    <div class="kpi-change positive">
                        <?php 
                        $porcentaje_completos = $stats['total_prospectos'] > 0 
                            ? round(($stats['prospectos_completos'] / $stats['total_prospectos']) * 100, 1)
                            : 0;
                        echo $porcentaje_completos . '%';
                        ?>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="dashboard-card kpi-card h-100">
                    <div class="stat-icon yellow mx-auto">
                        <i class="bi bi-file-earmark-text-fill"></i>
                    </div>
                    <div class="kpi-value"><?php echo number_format($stats['total_documentos']); ?></div>
                    <div class="kpi-label">Total Documentos</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="dashboard-card kpi-card h-100">
                    <div class="stat-icon purple mx-auto">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    <div class="kpi-value"><?php echo $stats['completitud_promedio']; ?>%</div>
                    <div class="kpi-label">Completitud Promedio</div>
                </div>
            </div>
        </div>

        <!-- Segunda fila: Gráficos -->
        <div class="row mb-4">
            <!-- Gráfico de evolución - 4 columnas -->
            <div class="col-md-4">
                <div class="dashboard-card h-100">
                    <h5 class="mb-3">
                        <i class="bi bi-bar-chart me-2"></i>
                        Evolución Diaria
                    </h5>
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="evolucionChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- Gráfico de tipo de persona - 4 columnas -->
            <div class="col-md-4">
                <div class="dashboard-card h-100">
                    <h5 class="mb-3">
                        <i class="bi bi-pie-chart me-2"></i>
                        Distribución por Tipo
                    </h5>
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="tipoPersonaChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- Gráfico de ejecutivos - 4 columnas -->
            <div class="col-md-4">
                <div class="dashboard-card h-100">
                    <h5 class="mb-3">
                        <i class="bi bi-people me-2"></i>
                        Top Ejecutivos
                    </h5>
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="ejecutivosChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contenedor de tabla y panel lateral -->
        <div class="table-panel-wrapper">
            <!-- Tabla de prospectos -->
            <div class="table-container" id="tableContainer">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="mb-0">
                        <i class="bi bi-list-task me-2"></i>
                        Lista de Prospectos
                    </h5>
                    <button onclick="exportarProspectos()" class="btn btn-success btn-sm" id="btnExportar">
                        <i class="bi bi-file-excel me-1"></i> Exportar a Excel
                    </button>
                </div>
                
                <div class="table-responsive">
                    <table id="prospectosTable" class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Razón Social</th>
                            <th>RUT</th>
                            <th>Tipo</th>
                            <th>Ejecutivo</th>
                            <th>Documentos</th>
                            <th>Completitud</th>
                            <th>Fecha Registro</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($prospectos_lista as $prospecto): ?>
                            <tr>
                                <td><?php echo $prospecto['id']; ?></td>
                                <td><?php echo htmlspecialchars($prospecto['razon_social']); ?></td>
                                <td><?php echo htmlspecialchars($prospecto['rut_cliente']); ?></td>
                                <td>
                                    <span class="badge badge-<?php echo strtolower($prospecto['tipo_persona']); ?>">
                                        <?php echo $prospecto['tipo_persona']; ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($prospecto['ejecutivo_nombre_completo']); ?></td>
                                <td>
                                    <?php echo $prospecto['obligatorios_completados']; ?>/<?php echo $prospecto['total_obligatorios']; ?>
                                    <small class="text-muted">(Total: <?php echo $prospecto['total_documentos']; ?>)</small>
                                </td>
                                <td>
                                    <div class="progress-custom" style="height: 10px;">
                                        <div class="progress-bar-custom <?php 
                                            if ($prospecto['porcentaje_completado'] < 50) {
                                                echo 'red';
                                            } elseif ($prospecto['porcentaje_completado'] < 80) {
                                                echo 'yellow';
                                            } else {
                                                echo 'green';
                                            }
                                        ?>" style="width: <?php echo $prospecto['porcentaje_completado']; ?>%"></div>
                                    </div>
                                    <small class="text-muted"><?php echo $prospecto['porcentaje_completado']; ?>%</small>
                                </td>
                                <td><?php echo date('d/m/Y', strtotime($prospecto['fecha_registro'])); ?></td>
                                <td>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-primary btn-ver-prospecto" 
                                            data-prospecto-id="<?php echo $prospecto['id']; ?>"
                                            title="Ver documentos">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                </div>
            </div><!-- Cierre de table-container -->
            
            <!-- Panel lateral para documentos -->
            <div id="documentsSidebar" class="documents-sidebar">
                <div class="sidebar-header">
                    <h5 class="sidebar-title">
                        <i class="bi bi-files me-2"></i>
                        <span id="sidebarProspectoNombre">Documentos del Prospecto</span>
                    </h5>
                    <button type="button" class="btn-close" onclick="cerrarPanelLateral()"></button>
                </div>
                
                <div class="sidebar-body">
                    <!-- Información del prospecto -->
                    <div class="prospecto-info-panel mb-4">
                        <div class="info-panel-header">
                            <h6 class="info-panel-title">
                                <i class="bi bi-person-circle me-2"></i>
                                Información del Prospecto
                            </h6>
                        </div>
                        <div class="info-panel-content">
                            <div class="row g-2">
                                <!-- Columna 1: Información Básica -->
                                <div class="col-4">
                                    <div class="info-column">
                                        <div class="info-item">
                                            <span class="info-label">ID:</span>
                                            <span id="sidebarProspectoId" class="info-value">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Usuario ID:</span>
                                            <span id="sidebarProspectoUsuarioId" class="info-value">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Nombre Ejecutivo:</span>
                                            <span id="sidebarProspectoEjecutivo" class="info-value">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">RUT Cliente:</span>
                                            <span id="sidebarProspectoRUT" class="info-value">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Razón Social:</span>
                                            <span id="sidebarProspectoRazonSocial" class="info-value">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Rubro:</span>
                                            <span id="sidebarProspectoRubro" class="info-value">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Dirección Comercial:</span>
                                            <span id="sidebarProspectoDireccion" class="info-value">-</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Columna 2: Información de Contacto -->
                                <div class="col-4">
                                    <div class="info-column">
                                        <div class="info-item">
                                            <span class="info-label">Teléfono Celular:</span>
                                            <span id="sidebarProspectoTelefono" class="info-value">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Email:</span>
                                            <span id="sidebarProspectoEmail" class="info-value">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Número POS:</span>
                                            <span id="sidebarProspectoNumeroPos" class="info-value">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Tipo Cuenta:</span>
                                            <span id="sidebarProspectoTipoCuenta" class="info-value">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Número Cuenta Bancaria:</span>
                                            <span id="sidebarProspectoNumeroCuenta" class="info-value">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Días Atención:</span>
                                            <span id="sidebarProspectoDiasAtencion" class="info-value">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Horario Atención:</span>
                                            <span id="sidebarProspectoHorarioAtencion" class="info-value">-</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Columna 3: Información Adicional -->
                                <div class="col-4">
                                    <div class="info-column">
                                        <div class="info-item">
                                            <span class="info-label">Contrata Boleta:</span>
                                            <span id="sidebarProspectoContrataBoleta" class="info-value">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Competencia Actual:</span>
                                            <span id="sidebarProspectoCompetencia" class="info-value">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Tipo Persona:</span>
                                            <span id="sidebarProspectoTipo" class="info-value">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Documentos Adjuntos:</span>
                                            <span id="sidebarProspectoDocumentosAdjuntos" class="info-value">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Fecha Registro:</span>
                                            <span id="sidebarProspectoFechaRegistro" class="info-value">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Fecha Actualización:</span>
                                            <span id="sidebarProspectoFechaActualizacion" class="info-value">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tabs -->
                    <ul class="nav nav-tabs" id="documentTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="documentos-tab" data-bs-toggle="tab" 
                                    data-bs-target="#documentos" type="button" role="tab">
                                <i class="bi bi-files me-2"></i>
                                Documentos (<span id="docCount">0</span>)
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="checklist-tab" data-bs-toggle="tab" 
                                    data-bs-target="#checklist" type="button" role="tab">
                                <i class="bi bi-check2-square me-2"></i>
                                Checklist
                            </button>
                        </li>
                    </ul>

                    <!-- Tab content -->
                    <div class="tab-content mt-3" id="documentTabsContent">
                        <!-- Tab de documentos -->
                        <div class="tab-pane fade show active" id="documentos" role="tabpanel">
                            <div id="documentosContent" class="documents-container">
                                <div class="text-center py-5">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Cargando...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Tab de checklist -->
                        <div class="tab-pane fade" id="checklist" role="tabpanel">
                            <div id="checklistContent" class="checklist-container">
                                <div class="text-center py-5">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Cargando...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div><!-- Cierre de table-panel-wrapper -->
    </div><!-- Cierre de container -->

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script type="text/javascript" src="https://cdn.datatables.net/v/bs5/jq-3.6.0/dt-1.11.5/datatables.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom JS con versionado -->
    <script src="js/dashboard.js?v=<?php echo time(); ?>"></script>

    <script>
        // Sistema de detección de versión para desarrollo - EJECUTAR PRIMERO
        (function() {
            const currentVersion = '<?php echo time(); ?>';
            const storageKey = 'inteletgroup_dashboard_version';
            const lastVersion = localStorage.getItem(storageKey);
            
            if (lastVersion && lastVersion !== currentVersion) {
                console.log('Nueva versión detectada en Dashboard. Limpiando caché...');
                // Limpiar caché del navegador
                if ('caches' in window) {
                    caches.keys().then(names => {
                        names.forEach(name => caches.delete(name));
                    });
                }
                // Actualizar versión almacenada
                localStorage.setItem(storageKey, currentVersion);
                // Forzar recarga completa
                window.location.reload(true);
            } else {
                localStorage.setItem(storageKey, currentVersion);
            }
        })();
        
        // Log de información del dashboard
        console.log('InteletGroup Dashboard - Versión:', '<?php echo date("Y-m-d H:i:s"); ?>');
        console.log('Usuario:', '<?php echo htmlspecialchars($usuario_logueado_nombre); ?>');
        
        // Deshabilitar caché para peticiones AJAX
        if (typeof $ !== 'undefined') {
            $.ajaxSetup({
                cache: false
            });
        }
        
        // DataTable se inicializa en js/dashboard.js para evitar duplicación

        // Función para prevenir caché del navegador
        function preventCache() {
            // Agregar timestamp a todos los enlaces internos
            document.querySelectorAll('a[href]').forEach(function(link) {
                if (link.href && link.href.indexOf(window.location.hostname) !== -1) {
                    const url = new URL(link.href);
                    url.searchParams.set('_t', Date.now());
                    link.href = url.toString();
                }
            });

            // Forzar recarga de CSS si es necesario
            const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
            cssLinks.forEach(function(link) {
                if (link.href.indexOf('css/') !== -1) {
                    const url = new URL(link.href);
                    url.searchParams.set('v', '<?php echo $version; ?>');
                    link.href = url.toString();
                }
            });
        }

        // Ejecutar al cargar la página
        document.addEventListener('DOMContentLoaded', preventCache);

        // Función adicional para limpiar DataTables duplicados
        function cleanupDataTables() {
            if (typeof $ !== 'undefined' && typeof $.fn.DataTable !== 'undefined') {
                // Destruir cualquier instancia existente de DataTable
                if ($.fn.DataTable.isDataTable('#prospectosTable')) {
                    $('#prospectosTable').DataTable().destroy();
                    console.log('DataTable duplicado limpiado');
                }
            }
        }

        // Ejecutar limpieza antes de que se cargue dashboard.js
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(cleanupDataTables, 100);
        });

        // Mostrar/ocultar campos de fecha personalizada
        document.querySelector('select[name="periodo"]').addEventListener('change', function() {
            const personalizado = this.value === 'personalizado';
            document.getElementById('fecha_inicio_container').style.display = personalizado ? 'block' : 'none';
            document.getElementById('fecha_fin_container').style.display = personalizado ? 'block' : 'none';
        });

        // Configuración de gráficos
        const chartColors = {
            primary: '#3b82f6',
            success: '#10b981',
            warning: '#f59e0b',
            danger: '#ef4444',
            secondary: '#6b7280',
            purple: '#8b5cf6'
        };

        // Gráfico de evolución diaria
        const evolucionData = <?php echo json_encode($stats['evolucion_mensual']); ?>;
        const evolucionCtx = document.getElementById('evolucionChart').getContext('2d');

        // Verificar si hay datos y si el elemento existe
        const evolucionChartElement = document.getElementById('evolucionChart');
        if (evolucionData && evolucionData.length > 0 && evolucionChartElement) {
            new Chart(evolucionCtx, {
                type: 'line',
                data: {
                    labels: evolucionData.map(item => {
                        const fecha = new Date(item.dia);
                        return fecha.getDate() + '/' + (fecha.getMonth() + 1);
                    }),
                    datasets: [{
                        label: 'Prospectos',
                        data: evolucionData.map(item => item.prospectos),
                        borderColor: chartColors.primary,
                        backgroundColor: chartColors.primary + '20',
                        tension: 0.4,
                        pointRadius: 5,
                        pointHoverRadius: 8
                    }, {
                        label: 'Documentos',
                        data: evolucionData.map(item => item.documentos),
                        borderColor: chartColors.success,
                        backgroundColor: chartColors.success + '20',
                        tension: 0.4,
                        pointRadius: 5,
                        pointHoverRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                boxWidth: 8
                            }
                        },
                        tooltip: {
                            callbacks: {
                                title: function(tooltipItems) {
                                    const index = tooltipItems[0].dataIndex;
                                    return 'Fecha: ' + evolucionData[index].dia;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        } else {
            // Mostrar mensaje si no hay datos
            const evolucionChartElement = document.getElementById('evolucionChart');
            if (evolucionChartElement && evolucionChartElement.parentNode) {
                evolucionChartElement.parentNode.innerHTML = '<p class="text-center text-muted mt-5">No hay datos disponibles para el período seleccionado</p>';
            }
        }

        // Gráfico de tipo de persona
        const tipoPersonaData = <?php echo json_encode($stats['prospectos_por_tipo']); ?>;
        const tipoPersonaChartElement = document.getElementById('tipoPersonaChart');
        
        // Verificar si hay datos y si el elemento existe
        if (tipoPersonaData && Object.keys(tipoPersonaData).length > 0 && tipoPersonaChartElement) {
            const tipoPersonaCtx = tipoPersonaChartElement.getContext('2d');
            new Chart(tipoPersonaCtx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(tipoPersonaData),
                    datasets: [{
                        data: Object.values(tipoPersonaData),
                        backgroundColor: [chartColors.primary, chartColors.success],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                boxWidth: 8
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        } else {
            // Mostrar mensaje si no hay datos
            if (tipoPersonaChartElement && tipoPersonaChartElement.parentNode) {
                tipoPersonaChartElement.parentNode.innerHTML = '<p class="text-center text-muted mt-5">No hay datos disponibles para el período seleccionado</p>';
            }
        }

        // Gráfico de ejecutivos
        const ejecutivosData = <?php echo json_encode($stats['prospectos_por_ejecutivo']); ?>;
        const ejecutivosChartElement = document.getElementById('ejecutivosChart');

        // Verificar si hay datos y si el elemento existe
        if (ejecutivosData && ejecutivosData.length > 0 && ejecutivosChartElement) {
            const ejecutivosCtx = ejecutivosChartElement.getContext('2d');
            new Chart(ejecutivosCtx, {
                type: 'bar',
                data: {
                    labels: ejecutivosData.map(item => item.nombre),
                    datasets: [{
                        label: 'Prospectos',
                        data: ejecutivosData.map(item => item.prospectos),
                        backgroundColor: chartColors.primary,
                        borderWidth: 1,
                        borderRadius: 4
                    }, {
                        label: 'Documentos',
                        data: ejecutivosData.map(item => item.documentos),
                        backgroundColor: chartColors.success,
                        borderWidth: 1,
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                boxWidth: 8
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: ${value}`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                drawBorder: false
                            }
                        },
                        y: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        } else {
            // Mostrar mensaje si no hay datos
            if (ejecutivosChartElement && ejecutivosChartElement.parentNode) {
                ejecutivosChartElement.parentNode.innerHTML = '<p class="text-center text-muted mt-5">No hay datos disponibles para el período seleccionado</p>';
            }
        }

        // Mejorar manejo de filtros para evitar problemas de caché
        function submitFilterForm() {
            const form = document.getElementById('filterForm');
            const formData = new FormData(form);

            // Agregar timestamp para evitar caché
            formData.append('_t', Date.now());

            // Construir URL con parámetros
            const params = new URLSearchParams();
            for (let [key, value] of formData.entries()) {
                params.append(key, value);
            }

            // Redirigir con los nuevos parámetros
            window.location.href = window.location.pathname + '?' + params.toString();
        }

        // Función para exportar prospectos dinámicamente - ACTUALIZADA PARA XLSX
        function exportarProspectos() {
            console.log('Función exportarProspectos ejecutada - versión XLSX');
            const btnExportar = document.getElementById('btnExportar');
            const iconoOriginal = btnExportar.innerHTML;

            // Cambiar estado del botón
            btnExportar.disabled = true;
            btnExportar.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> Exportando XLSX...';

            // Obtener los filtros actuales
            const params = new URLSearchParams(window.location.search);

            // Crear formulario invisible para descarga Excel (detección automática)
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'inteletgroup_export_smart.php';
            form.style.display = 'none';

            console.log('Enviando a: inteletgroup_export_smart.php (detección automática de formato)');
            
            // Agregar parámetros de filtro al formulario
            for (const [key, value] of params) {
                if (key !== '_t') { // Excluir el timestamp
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = key;
                    input.value = value;
                    form.appendChild(input);
                }
            }
            
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
            
            // Restaurar estado del botón después de un momento
            setTimeout(() => {
                btnExportar.disabled = false;
                btnExportar.innerHTML = iconoOriginal;
            }, 2000);
        }

        // Reemplazar los eventos onchange por eventos más robustos
        document.addEventListener('DOMContentLoaded', function() {
            const ejecutivoSelect = document.querySelector('select[name="ejecutivo"]');
            const periodoSelect = document.querySelector('select[name="periodo"]');

            if (ejecutivoSelect) {
                ejecutivoSelect.removeAttribute('onchange');
                ejecutivoSelect.addEventListener('change', function() {
                    submitFilterForm();
                });
            }

            if (periodoSelect) {
                periodoSelect.removeAttribute('onchange');
                periodoSelect.addEventListener('change', function() {
                    submitFilterForm();
                });
            }
        });
    </script>

    <script>
        // Funciones para el panel lateral
        function abrirPanelLateral(prospectoId) {
            const sidebar = document.getElementById('documentsSidebar');
            const tableContainer = document.getElementById('tableContainer');
            
            // Activar panel y comprimir tabla
            sidebar.classList.add('active');
            tableContainer.classList.add('sidebar-active');
            
            // Mostrar spinner mientras se cargan los datos
            mostrarSpinnerEnPanel();
            
            // Cargar datos del prospecto
            cargarDatosProspecto(prospectoId);
        }
        
        function mostrarSpinnerEnPanel() {
            const documentosContent = document.getElementById('documentosContent');
            const checklistContent = document.getElementById('checklistContent');
            
            const spinnerHtml = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                    <p class="mt-2 text-muted">Cargando documentos...</p>
                </div>
            `;
            
            documentosContent.innerHTML = spinnerHtml;
            checklistContent.innerHTML = spinnerHtml;
        }

        function cerrarPanelLateral() {
            const sidebar = document.getElementById('documentsSidebar');
            const tableContainer = document.getElementById('tableContainer');
            
            // Desactivar panel y expandir tabla
            sidebar.classList.remove('active');
            tableContainer.classList.remove('sidebar-active');
            
            // Limpiar contenido después de la animación
            setTimeout(() => {
                const documentosContent = document.getElementById('documentosContent');
                const checklistContent = document.getElementById('checklistContent');
                if (documentosContent) {
                    documentosContent.innerHTML = '<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Cargando...</span></div></div>';
                }
                if (checklistContent) {
                    checklistContent.innerHTML = '<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Cargando...</span></div></div>';
                }
                // Resetear tabs al primero
                const documentosTab = document.getElementById('documentos-tab');
                if (documentosTab) {
                    documentosTab.click();
                }
            }, 300);
        }

        function cargarDatosProspecto(prospectoId) {
            // Hacer petición AJAX para obtener datos del prospecto
            fetch(`inteletgroup_ajax_documentos.php?action=getProspectoInfo&prospecto_id=${prospectoId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Actualizar información del prospecto
                        document.getElementById('sidebarProspectoNombre').textContent = data.prospecto.razon_social;
                        
                        // Columna 1: Información Básica
                        document.getElementById('sidebarProspectoId').textContent = data.prospecto.id || '-';
                        document.getElementById('sidebarProspectoUsuarioId').textContent = data.prospecto.usuario_id || '-';
                        document.getElementById('sidebarProspectoEjecutivo').textContent = data.prospecto.ejecutivo_nombre || '-';
                        document.getElementById('sidebarProspectoRUT').textContent = data.prospecto.rut_cliente || '-';
                        document.getElementById('sidebarProspectoRazonSocial').textContent = data.prospecto.razon_social || '-';
                        document.getElementById('sidebarProspectoRubro').textContent = data.prospecto.rubro || '-';
                        document.getElementById('sidebarProspectoDireccion').textContent = data.prospecto.direccion_comercial || '-';
                        
                        // Columna 2: Información de Contacto
                        document.getElementById('sidebarProspectoTelefono').textContent = data.prospecto.telefono_celular || '-';
                        document.getElementById('sidebarProspectoEmail').textContent = data.prospecto.email || '-';
                        document.getElementById('sidebarProspectoNumeroPos').textContent = data.prospecto.numero_pos || '-';
                        document.getElementById('sidebarProspectoTipoCuenta').textContent = data.prospecto.tipo_cuenta || '-';
                        document.getElementById('sidebarProspectoNumeroCuenta').textContent = data.prospecto.numero_cuenta_bancaria || '-';
                        document.getElementById('sidebarProspectoDiasAtencion').textContent = data.prospecto.dias_atencion || '-';
                        document.getElementById('sidebarProspectoHorarioAtencion').textContent = data.prospecto.horario_atencion || '-';
                        
                        // Columna 3: Información Adicional
                        document.getElementById('sidebarProspectoContrataBoleta').textContent = data.prospecto.contrata_boleta || '-';
                        document.getElementById('sidebarProspectoCompetencia').textContent = data.prospecto.competencia_actual || '-';
                        document.getElementById('sidebarProspectoTipo').textContent = data.prospecto.tipo_persona || '-';
                        document.getElementById('sidebarProspectoDocumentosAdjuntos').textContent = data.prospecto.documentos_adjuntos || '-';
                        document.getElementById('sidebarProspectoFechaRegistro').textContent = data.prospecto.fecha_registro || '-';
                        document.getElementById('sidebarProspectoFechaActualizacion').textContent = data.prospecto.fecha_actualizacion || '-';
                        
                        // Cargar documentos y checklist
                        cargarDocumentos(prospectoId);
                        cargarChecklist(prospectoId);
                    }
                })
                .catch(error => {
                    console.error('Error cargando datos del prospecto:', error);
                    mostrarErrorEnPanel('Error al cargar los datos del prospecto');
                });
        }

        function cargarDocumentos(prospectoId) {
            fetch(`inteletgroup_ajax_documentos.php?action=getDocumentosVisualizacion&prospecto_id=${prospectoId}`)
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('documentosContent');
                    if (data.success && data.documentos.length > 0) {
                        container.innerHTML = `
                            <!-- Controles de selección -->
                            <div class="selection-controls">
                                <div class="row g-2">
                                    <div class="col-auto">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleAllDocumentsSidebar()">
                                            <i class="bi bi-check-all me-1"></i>
                                            Seleccionar todos
                                        </button>
                                    </div>
                                    <div class="col-auto">
                                        <button type="button" class="btn btn-sm btn-success" onclick="downloadSelectedDocuments()">
                                            <i class="bi bi-download me-1"></i>
                                            Descargar seleccionados
                                        </button>
                                    </div>
                                    <div class="col-auto">
                                        <small class="text-muted align-self-center">
                                            <span id="selectedCount">0</span> de ${data.documentos.length} seleccionados
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <form id="sidebarDocumentsForm">
                                <input type="hidden" name="prospecto_id" value="${prospectoId}">
                                <div class="document-grid">
                                    ${data.documentos.map(doc => {
                                        const extension = doc.tipo_archivo.toLowerCase();
                                        let docClass = '';
                                        let iconClass = 'bi-file-earmark';
                                        
                                        if (extension.includes('pdf')) {
                                            docClass = 'pdf';
                                            iconClass = 'bi-file-earmark-pdf';
                                        } else if (extension.includes('doc') || extension.includes('docx')) {
                                            docClass = 'doc';
                                            iconClass = 'bi-file-earmark-word';
                                        } else if (extension.includes('jpg') || extension.includes('jpeg') || extension.includes('png')) {
                                            docClass = 'image';
                                            iconClass = 'bi-file-earmark-image';
                                        } else if (extension.includes('xls') || extension.includes('xlsx')) {
                                            docClass = 'excel';
                                            iconClass = 'bi-file-earmark-excel';
                                        }
                                        
                                        return `
                                            <div class="document-card ${docClass}">
                                                <input type="checkbox" name="selected_docs[]" value="${doc.id}" 
                                                       class="form-check-input document-checkbox" 
                                                       onchange="updateSelectedCount()">
                                                       
                                                ${doc.tipo_documento_nombre ? `
                                                    <div class="document-type-title">
                                                        ${doc.tipo_documento_nombre}
                                                    </div>
                                                ` : `
                                                    <div class="document-type-title text-muted">
                                                        Documento sin clasificar
                                                    </div>
                                                `}
                                                
                                                <div class="document-icon mb-2">
                                                    <i class="bi ${iconClass}"></i>
                                                </div>
                                                
                                                <div class="document-filename mb-2" title="${doc.nombre_original}">
                                                    ${doc.nombre_original}
                                                </div>
                                                
                                                <p class="text-muted small mb-2">
                                                    ${formatFileSize(doc.tamaño_archivo)}
                                                </p>
                                                
                                                <p class="text-muted small mb-3">
                                                    <i class="bi bi-calendar3 me-1"></i>
                                                    ${new Date(doc.fecha_subida).toLocaleDateString('es-ES')}
                                                </p>
                                                
                                                <div class="mt-auto">
                                                    <div class="d-grid gap-1">
                                                        <a href="descargar_documento.php?id=${doc.id}&action=view"
                                                           class="btn btn-sm btn-primary" target="_blank">
                                                            <i class="bi bi-eye me-1"></i>
                                                            Ver
                                                        </a>
                                                        <a href="descargar_documento.php?id=${doc.id}&action=download"
                                                           class="btn btn-sm btn-outline-primary">
                                                            <i class="bi bi-download me-1"></i>
                                                            Descargar
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        `;
                                    }).join('')}
                                </div>
                            </form>
                        `;
                        
                        // Inicializar contador
                        updateSelectedCount();
                    } else {
                        container.innerHTML = `
                            <div class="empty-state">
                                <i class="bi bi-file-earmark empty-state-icon"></i>
                                <p class="empty-state-text">No hay documentos subidos</p>
                            </div>
                        `;
                    }
                    
                    // Actualizar contador de documentos
                    document.getElementById('docCount').textContent = data.documentos ? data.documentos.length : 0;
                })
                .catch(error => {
                    console.error('Error cargando documentos:', error);
                    document.getElementById('documentosContent').innerHTML = '<div class="text-danger text-center py-4">Error al cargar documentos</div>';
                });
        }

        function cargarChecklist(prospectoId) {
            fetch(`inteletgroup_ajax_documentos.php?action=getChecklist&prospecto_id=${prospectoId}`)
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('checklistContent');
                    if (data.success && data.checklist) {
                        const checklist = data.checklist;
                        const documentosSubidos = checklist.filter(item => item.estado === 'Subido').length;
                        const documentosRequeridos = checklist.filter(item => item.es_requerido).length;
                        const requeridosSubidos = checklist.filter(item => item.es_requerido && item.estado === 'Subido').length;
                        const porcentajeRequeridos = documentosRequeridos > 0 ? Math.round((requeridosSubidos / documentosRequeridos) * 100) : 0;
                        const porcentajeTotal = checklist.length > 0 ? Math.round((documentosSubidos / checklist.length) * 100) : 0;
                        
                        container.innerHTML = `
                            <!-- Resumen del checklist -->
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <div class="row text-center">
                                            <div class="col-md-3">
                                                <h5 class="mb-1">${documentosSubidos}/${checklist.length}</h5>
                                                <small>Total Documentos</small>
                                            </div>
                                            <div class="col-md-3">
                                                <h5 class="mb-1">${requeridosSubidos}/${documentosRequeridos}</h5>
                                                <small>Requeridos</small>
                                            </div>
                                            <div class="col-md-3">
                                                <h5 class="mb-1 text-success">${porcentajeRequeridos}%</h5>
                                                <small>Completitud Requeridos</small>
                                            </div>
                                            <div class="col-md-3">
                                                <h5 class="mb-1 text-primary">${porcentajeTotal}%</h5>
                                                <small>Completitud Total</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Tabla de Documentos Requeridos -->
                            <div class="mb-4">
                                <h6 class="mb-3">
                                    <i class="bi bi-exclamation-triangle-fill text-warning me-2"></i>
                                    Documentos Requeridos (${requeridosSubidos}/${documentosRequeridos})
                                </h6>
                                <div class="table-responsive">
                                    <table class="table table-hover checklist-table">
                                        <thead>
                                            <tr>
                                                <th width="10%">Estado</th>
                                                <th width="45%">Tipo de Documento</th>
                                                <th width="20%">Fecha</th>
                                                <th width="25%">Acciones</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${checklist.filter(item => item.es_requerido).map(item => `
                                                <tr class="${item.estado === 'Subido' ? 'table-success' : 'table-warning'}">
                                                    <td class="text-center">
                                                        ${item.estado === 'Subido' ? 
                                                            '<i class="bi bi-check-circle-fill text-success status-icon"></i>' : 
                                                            '<i class="bi bi-x-circle-fill text-danger status-icon"></i>'
                                                        }
                                                    </td>
                                                    <td>
                                                        <strong>${item.nombre}</strong>
                                                        <br>
                                                        <small class="text-muted">${item.descripcion || ''}</small>
                                                    </td>
                                                    <td>
                                                        ${item.estado === 'Subido' && item.documento_info ? 
                                                            `<small class="text-muted">${new Date(item.documento_info.fecha_subida).toLocaleDateString('es-ES')}</small>` : 
                                                            '<small class="text-muted">-</small>'
                                                        }
                                                    </td>
                                                    <td>
                                                        ${item.estado === 'Subido' && item.documento_info ? `
                                                            <div class="btn-group btn-group-sm">
                                                                <a href="descargar_documento.php?id=${item.documento_info.id}&action=view"
                                                                   class="btn btn-outline-primary btn-sm" target="_blank" title="Ver documento">
                                                                    <i class="bi bi-eye"></i> Ver
                                                                </a>
                                                                <a href="descargar_documento.php?id=${item.documento_info.id}&action=download"
                                                                   class="btn btn-outline-secondary btn-sm" title="Descargar">
                                                                    <i class="bi bi-download"></i> Descargar
                                                                </a>
                                                            </div>
                                                        ` : `
                                                            <button type="button" class="btn btn-outline-primary btn-sm" disabled>
                                                                <i class="bi bi-upload"></i> Pendiente
                                                            </button>
                                                        `}
                                                    </td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Tabla de Documentos Opcionales -->
                            <div class="mb-4">
                                <h6 class="mb-3">
                                    <i class="bi bi-info-circle-fill text-info me-2"></i>
                                    Documentos Opcionales (${checklist.filter(item => !item.es_requerido && item.estado === 'Subido').length}/${checklist.filter(item => !item.es_requerido).length})
                                </h6>
                                <div class="table-responsive">
                                    <table class="table table-hover checklist-table">
                                        <thead>
                                            <tr>
                                                <th width="10%">Estado</th>
                                                <th width="45%">Tipo de Documento</th>
                                                <th width="20%">Fecha</th>
                                                <th width="25%">Acciones</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${checklist.filter(item => !item.es_requerido).map(item => `
                                                <tr class="${item.estado === 'Subido' ? 'table-success' : ''}">
                                                    <td class="text-center">
                                                        ${item.estado === 'Subido' ? 
                                                            '<i class="bi bi-check-circle-fill text-success status-icon"></i>' : 
                                                            '<i class="bi bi-circle text-muted status-icon"></i>'
                                                        }
                                                    </td>
                                                    <td>
                                                        <strong>${item.nombre}</strong>
                                                        <br>
                                                        <small class="text-muted">${item.descripcion || ''}</small>
                                                    </td>
                                                    <td>
                                                        ${item.estado === 'Subido' && item.documento_info ? 
                                                            `<small class="text-muted">${new Date(item.documento_info.fecha_subida).toLocaleDateString('es-ES')}</small>` : 
                                                            '<small class="text-muted">-</small>'
                                                        }
                                                    </td>
                                                    <td>
                                                        ${item.estado === 'Subido' && item.documento_info ? `
                                                            <div class="btn-group btn-group-sm">
                                                                <a href="descargar_documento.php?id=${item.documento_info.id}&action=view"
                                                                   class="btn btn-outline-primary btn-sm" target="_blank" title="Ver documento">
                                                                    <i class="bi bi-eye"></i> Ver
                                                                </a>
                                                                <a href="descargar_documento.php?id=${item.documento_info.id}&action=download"
                                                                   class="btn btn-outline-secondary btn-sm" title="Descargar">
                                                                    <i class="bi bi-download"></i> Descargar
                                                                </a>
                                                            </div>
                                                        ` : `
                                                            <button type="button" class="btn btn-outline-secondary btn-sm" disabled>
                                                                <i class="bi bi-upload"></i> Opcional
                                                            </button>
                                                        `}
                                                    </td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        `;
                    } else {
                        container.innerHTML = `
                            <div class="empty-state">
                                <i class="bi bi-list-check empty-state-icon"></i>
                                <p class="empty-state-text">No hay checklist disponible</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error cargando checklist:', error);
                    const container = document.getElementById('checklistContent');
                    if (container) {
                        container.innerHTML = '<div class="text-danger text-center py-4">Error al cargar checklist</div>';
                    }
                });
        }

        function getDocumentIcon(tipoArchivo) {
            const extension = tipoArchivo.toLowerCase();
            if (extension.includes('pdf')) return 'bi-file-earmark-pdf';
            if (extension.includes('doc') || extension.includes('docx')) return 'bi-file-earmark-word';
            if (extension.includes('xls') || extension.includes('xlsx')) return 'bi-file-earmark-excel';
            if (extension.includes('jpg') || extension.includes('jpeg') || extension.includes('png')) return 'bi-file-earmark-image';
            return 'bi-file-earmark';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function descargarTodos(prospectoId) {
            window.location.href = `inteletgroup_ajax_documentos.php?action=downloadAll&prospecto_id=${prospectoId}`;
        }

        function mostrarErrorEnPanel(mensaje) {
            const documentosContent = document.getElementById('documentosContent');
            const checklistContent = document.getElementById('checklistContent');
            if (documentosContent) {
                documentosContent.innerHTML = `<div class="text-danger text-center py-4">${mensaje}</div>`;
            }
            if (checklistContent) {
                checklistContent.innerHTML = `<div class="text-danger text-center py-4">${mensaje}</div>`;
            }
        }

        // Funciones para selección múltiple de documentos en el sidebar
        function toggleAllDocumentsSidebar() {
            const checkboxes = document.querySelectorAll('#sidebarDocumentsForm .document-checkbox');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);
            
            checkboxes.forEach(cb => {
                cb.checked = !allChecked;
            });
            
            updateSelectedCount();
        }
        
        function updateSelectedCount() {
            const checkboxes = document.querySelectorAll('#sidebarDocumentsForm .document-checkbox:checked');
            const countElement = document.getElementById('selectedCount');
            if (countElement) {
                countElement.textContent = checkboxes.length;
            }
        }
        
        function downloadSelectedDocuments() {
            const checkboxes = document.querySelectorAll('#sidebarDocumentsForm .document-checkbox:checked');
            
            if (checkboxes.length === 0) {
                alert('Por favor selecciona al menos un documento');
                return;
            }
            
            // Obtener IDs de documentos seleccionados
            const selectedIds = Array.from(checkboxes).map(cb => cb.value);
            
            // Crear formulario temporal para descarga
            const form = document.createElement('form');
            form.method = 'GET';
            form.action = 'inteletgroup_ajax_documentos.php';
            form.target = '_blank';
            
            // Agregar acción
            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'downloadAll';
            form.appendChild(actionInput);
            
            // Agregar prospecto_id
            const prospectoIdInput = document.querySelector('#sidebarDocumentsForm input[name="prospecto_id"]');
            if (prospectoIdInput) {
                const prospectoInput = document.createElement('input');
                prospectoInput.type = 'hidden';
                prospectoInput.name = 'prospecto_id';
                prospectoInput.value = prospectoIdInput.value;
                form.appendChild(prospectoInput);
            }
            
            // Agregar IDs seleccionados
            selectedIds.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'selected_docs[]';
                input.value = id;
                form.appendChild(input);
            });
            
            // Ejecutar descarga
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }
        
        // Función para formatear tamaño de archivos
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // Event listener para los botones de ver prospecto
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.btn-ver-prospecto').forEach(btn => {
                btn.addEventListener('click', function() {
                    const prospectoId = this.getAttribute('data-prospecto-id');
                    abrirPanelLateral(prospectoId);
                });
            });
            
            // Sistema de localStorage para navegación - Estrategia TQW
            if (typeof localStorage !== "undefined") {
                var currentPage = window.location.href;
                var previousPages = JSON.parse(localStorage.getItem("navigationHistory") || "[]");
                
                // Añadir página actual si no existe
                if (previousPages.indexOf(currentPage) === -1) {
                    previousPages.push(currentPage);
                    // Mantener solo últimas 5 páginas
                    if (previousPages.length > 5) {
                        previousPages = previousPages.slice(-5);
                    }
                    localStorage.setItem("navigationHistory", JSON.stringify(previousPages));
                }
                
                // Registrar estado de sesión para diagnóstico
                localStorage.setItem('ultimaSesionActiva', '<?php echo date('Y-m-d H:i:s'); ?>');
                localStorage.setItem('ultimoUsuarioActivo', '<?php echo $_SESSION['usuario_id'] ?? ''; ?>');
                localStorage.setItem('ultimaPaginaConSesion', window.location.href);
            }
            
            // Implementar funcionalidad del botón "Volver" si existe
            var btnVolver = document.querySelector('.btn-back, #btnVolver, a[href*="login.php"]');
            if (btnVolver) {
                btnVolver.addEventListener("click", function(e) {
                    // Si es un enlace de logout, no interferir
                    if (this.href && this.href.includes('login.php')) {
                        return true;
                    }
                    
                    e.preventDefault();
                    
                    // Prioridad 1: Historial del navegador
                    if (window.history.length > 1) {
                        window.history.go(-1);
                        return;
                    }
                    
                    // Prioridad 2: localStorage
                    if (typeof localStorage !== "undefined") {
                        var previousPages = JSON.parse(localStorage.getItem("navigationHistory") || "[]");
                        if (previousPages.length > 1) {
                            var prevPage = previousPages[previousPages.length - 2];
                            window.location.href = prevPage;
                            return;
                        }
                    }
                    
                    // Fallback: Página segura
                    window.location.href = "login.php";
                });
            }
        });
    </script>

    <!-- Script ultra agresivo para eliminar TODO el caché -->
    <script src="<?php echo version_url('js/no-cache.js'); ?>"></script>
</body>
</html>