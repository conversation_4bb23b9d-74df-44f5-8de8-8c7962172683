# Estrategia de Caché Controlado - TQW
# Permite navegación fluida con validación inteligente

# Configuración de headers para caché controlado
<IfModule mod_headers.c>
    # Headers por defecto - caché privado con revalidación
    Header set Cache-Control "private, must-revalidate"
    Header set Pragma "private"
    
    # Para archivos PHP - siempre validar
    <FilesMatch "\.php$">
        Header set Cache-Control "private, must-revalidate"
        Header set Pragma "private"
    </FilesMatch>
    
    # Para archivos estáticos - caché más largo pero con validación
    <FilesMatch "\.(js|css)$">
        Header set Cache-Control "private, max-age=3600, must-revalidate"
        Header append Vary "Accept-Encoding"
    </FilesMatch>
    
    # Para imágenes - caché más prolongado
    <FilesMatch "\.(jpg|jpeg|png|gif|ico)$">
        Header set Cache-Control "private, max-age=86400"
    </FilesMatch>
    
    # Mantener ETags para validación inteligente
    FileETag MTime Size
</IfModule>

# Configuración de sesiones PHP persistentes
<IfModule mod_php7.c>
    # Sesiones de 24 horas
    php_value session.gc_maxlifetime 86400
    php_value session.cookie_lifetime 86400
    # Caché de sesión privado
    php_value session.cache_limiter "private"
    php_value session.cache_expire "1440"
</IfModule>

# Habilitar compresión para mejor rendimiento
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript
    AddOutputFilterByType DEFLATE application/javascript application/x-javascript
    AddOutputFilterByType DEFLATE application/json application/xml application/xhtml+xml
</IfModule>

# Configuración de reescritura si es necesaria
<IfModule mod_rewrite.c>
    RewriteEngine On
    # Reglas adicionales aquí si son necesarias
</IfModule>