# CONFIGURACIÓN ULTRA AGRESIVA PARA ELIMINAR TODO EL CACHÉ
# Versión sin caché - Eliminar completamente

# Habilitar mod_headers si está disponible
<IfModule mod_headers.c>
    # Headers ultra agresivos para eliminar TODO tipo de caché
    Header always set Cache-Control "no-store, no-cache, must-revalidate, max-age=0, s-maxage=0, proxy-revalidate, no-transform, private"
    Header always set Pragma "no-cache"
    Header always set Expires "0"
    Header always set Expires "Mon, 26 Jul 1997 05:00:00 GMT"
    Header always set Last-Modified "Thu, 01 Jan 1970 00:00:00 GMT"
    Header always set Vary "*"
    Header always set X-Accel-Expires "0"
    Header always set X-Cache-Enabled "False"
    Header always set Surrogate-Control "no-store"
    Header always set Edge-Control "no-store"
    Header always set CF-Cache-Status "BYPASS"

    # ETag dinámico para forzar revalidación
    Header always set ETag "\"no-cache-%{TIME_YEAR}%{TIME_MON}%{TIME_DAY}%{TIME_HOUR}%{TIME_MIN}%{TIME_SEC}\""

    # Headers de seguridad
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # Header personalizado para debugging
    Header always set X-No-Cache-Htaccess "Applied"
</IfModule>

# Configuración específica para archivos PHP
<FilesMatch "\.(php)$">
    <IfModule mod_headers.c>
        Header always set Cache-Control "no-store, no-cache, must-revalidate, max-age=0, s-maxage=0, proxy-revalidate, no-transform, private"
        Header always set Pragma "no-cache"
        Header always set Expires "0"
    </IfModule>
</FilesMatch>

# Configuración específica para archivos CSS y JS
<FilesMatch "\.(css|js)$">
    <IfModule mod_headers.c>
        Header always set Cache-Control "no-store, no-cache, must-revalidate, max-age=0"
        Header always set Pragma "no-cache"
        Header always set Expires "0"
    </IfModule>
</FilesMatch>

# Configuración específica para imágenes
<FilesMatch "\.(jpg|jpeg|png|gif|ico|svg|webp)$">
    <IfModule mod_headers.c>
        Header always set Cache-Control "no-store, no-cache, must-revalidate, max-age=0"
        Header always set Pragma "no-cache"
        Header always set Expires "0"
    </IfModule>
</FilesMatch>

# Deshabilitar ETags del servidor
<IfModule mod_headers.c>
    Header unset ETag
</IfModule>
FileETag None

# Configuración para mod_expires (deshabilitar completamente)
<IfModule mod_expires.c>
    ExpiresActive Off
</IfModule>

# Configuración de sesiones PHP para evitar caché
<IfModule mod_php7.c>
    php_value session.cache_limiter nocache
    php_value session.cache_expire 0
    php_value opcache.enable 0
    php_value opcache.enable_cli 0
    php_value opcache.revalidate_freq 0
    php_value opcache.validate_timestamps 1
</IfModule>

<IfModule mod_php8.c>
    php_value session.cache_limiter nocache
    php_value session.cache_expire 0
    php_value opcache.enable 0
    php_value opcache.enable_cli 0
    php_value opcache.revalidate_freq 0
    php_value opcache.validate_timestamps 1
</IfModule>

# Prevenir caché de directorios
Options -Indexes

# Configuración específica para desarrollo local
<IfModule mod_setenvif.c>
    SetEnvIf Host "localhost" development=1
    SetEnvIf Host "127.0.0.1" development=1
    SetEnvIf Host "::1" development=1

    # Headers extra agresivos para desarrollo
    <IfModule mod_headers.c>
        Header always set X-Development-Mode "true" env=development
        Header always set Cache-Control "no-store, no-cache, must-revalidate, max-age=0, s-maxage=0, proxy-revalidate, no-transform, private" env=development
    </IfModule>
</IfModule>

# Reescritura para agregar parámetros anti-caché automáticamente
<IfModule mod_rewrite.c>
    RewriteEngine On

    # Agregar timestamp a recursos estáticos si no lo tienen
    RewriteCond %{QUERY_STRING} !nocache=
    RewriteCond %{REQUEST_FILENAME} \.(css|js|png|jpg|jpeg|gif|ico|svg)$
    RewriteRule ^(.*)$ $1?nocache=%{TIME}&rand=%{REMOTE_ADDR} [QSA,L]
</IfModule>

# Comentario final para debugging
# HTACCESS ANTI-CACHÉ APLICADO - NO GUARDAR NADA