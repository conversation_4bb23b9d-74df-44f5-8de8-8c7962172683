# CONFIGURACIÓN ANTI-CACHÉ SIMPLIFICADA PARA XAMPP
# Compatible con Apache en Windows

# Headers básicos anti-caché
<IfModule mod_headers.c>
    # Headers principales para eliminar caché
    Header set Cache-Control "no-store, no-cache, must-revalidate, max-age=0"
    Header set Pragma "no-cache"
    Header set Expires "0"

    # Headers de seguridad básicos
    Header set X-Frame-Options "SAMEORIGIN"
    Header set X-Content-Type-Options "nosniff"

    # Header de debugging
    Header set X-No-Cache-Applied "true"
</IfModule>

# Configuración específica para archivos PHP
<FilesMatch "\.(php)$">
    <IfModule mod_headers.c>
        Header set Cache-Control "no-store, no-cache, must-revalidate, max-age=0"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </IfModule>
</FilesMatch>

# Configuración específica para archivos CSS y JS
<FilesMatch "\.(css|js)$">
    <IfModule mod_headers.c>
        Header set Cache-Control "no-store, no-cache, must-revalidate, max-age=0"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </IfModule>
</FilesMatch>

# Deshabilitar ETags
FileETag None

# Deshabilitar expires
<IfModule mod_expires.c>
    ExpiresActive Off
</IfModule>

# Configuración básica de PHP
<IfModule mod_php7.c>
    php_value session.cache_limiter nocache
    php_value session.cache_expire 0
</IfModule>

<IfModule mod_php8.c>
    php_value session.cache_limiter nocache
    php_value session.cache_expire 0
</IfModule>

# Prevenir listado de directorios
Options -Indexes

# Reescritura básica
<IfModule mod_rewrite.c>
    RewriteEngine On
</IfModule>

# HTACCESS ANTI-CACHÉ APLICADO