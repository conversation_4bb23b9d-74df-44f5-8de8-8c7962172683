<?php
// Configuraciones de sesión ANTES de session_start() - Estrategia TQW
ini_set('session.use_cookies', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_lifetime', 86400); // 24 horas
ini_set('session.gc_maxlifetime', 86400);  // 24 horas

// Iniciar sesión si no está activa
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Headers para evitar caché
header('Content-Type: text/html; charset=UTF-8');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Pragma: no-cache');
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Debug Session y Tokens</title>
    <style>
        body { font-family: monospace; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .section { margin-bottom: 30px; }
        h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .actions { margin-top: 20px; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Debug de Sesión y Tokens - InteletGroup</h1>
        
        <div class="section">
            <h2>Información de Sesión</h2>
            <pre><?php 
                echo "Session ID: " . session_id() . "\n";
                echo "Session Status: " . session_status() . "\n";
                echo "Session Name: " . session_name() . "\n";
                echo "Cookie Params:\n";
                print_r(session_get_cookie_params());
            ?></pre>
        </div>
        
        <div class="section">
            <h2>Tokens Procesados</h2>
            <?php if (isset($_SESSION['processed_tokens']) && is_array($_SESSION['processed_tokens'])): ?>
                <p>Total tokens almacenados: <?php echo count($_SESSION['processed_tokens']); ?></p>
                <pre><?php 
                    $tokens = $_SESSION['processed_tokens'];
                    // Mostrar solo los últimos 10
                    $recent_tokens = array_slice($tokens, -10);
                    foreach ($recent_tokens as $index => $token) {
                        echo ($index + 1) . ". " . htmlspecialchars($token) . "\n";
                    }
                ?></pre>
            <?php else: ?>
                <p>No hay tokens procesados en la sesión.</p>
            <?php endif; ?>
        </div>
        
        <div class="section">
            <h2>Rate Limiting Keys</h2>
            <pre><?php 
                $rate_limit_keys = [];
                foreach ($_SESSION as $key => $value) {
                    if (strpos($key, 'inteletgroup_') === 0) {
                        $rate_limit_keys[$key] = [
                            'value' => $value,
                            'age' => microtime(true) - $value . ' segundos'
                        ];
                    }
                }
                print_r($rate_limit_keys);
            ?></pre>
        </div>
        
        <div class="section">
            <h2>Variables de Sesión</h2>
            <pre><?php 
                $session_copy = $_SESSION;
                // Ocultar tokens para no saturar la vista
                if (isset($session_copy['processed_tokens'])) {
                    $session_copy['processed_tokens'] = 'Array con ' . count($session_copy['processed_tokens']) . ' tokens';
                }
                print_r($session_copy);
            ?></pre>
        </div>
        
        <div class="section">
            <h2>localStorage (JavaScript)</h2>
            <div id="localStorage-info"></div>
        </div>
        
        <div class="actions">
            <h2>Acciones de Limpieza</h2>
            <form method="post" style="display: inline;">
                <button type="submit" name="clear_tokens" onclick="return confirm('¿Limpiar todos los tokens procesados?')">
                    Limpiar Tokens Procesados
                </button>
            </form>
            
            <form method="post" style="display: inline;">
                <button type="submit" name="clear_rate_limit" onclick="return confirm('¿Limpiar rate limiting?')">
                    Limpiar Rate Limiting
                </button>
            </form>
            
            <form method="post" style="display: inline;">
                <button type="submit" name="clear_all" onclick="return confirm('¿Limpiar toda la sesión?')">
                    Limpiar Toda la Sesión
                </button>
            </form>
        </div>
        
        <?php
        // Procesar acciones
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            if (isset($_POST['clear_tokens'])) {
                unset($_SESSION['processed_tokens']);
                echo '<p class="success">✓ Tokens procesados limpiados</p>';
            }
            
            if (isset($_POST['clear_rate_limit'])) {
                foreach ($_SESSION as $key => $value) {
                    if (strpos($key, 'inteletgroup_') === 0) {
                        unset($_SESSION[$key]);
                    }
                }
                echo '<p class="success">✓ Rate limiting limpiado</p>';
            }
            
            if (isset($_POST['clear_all'])) {
                session_destroy();
                echo '<p class="success">✓ Sesión destruida. Recarga la página para iniciar una nueva.</p>';
            }
            
            echo '<script>setTimeout(function(){ window.location.reload(); }, 2000);</script>';
        }
        ?>
    </div>
    
    <script>
        // Mostrar información de localStorage
        document.addEventListener('DOMContentLoaded', function() {
            const localStorageDiv = document.getElementById('localStorage-info');
            let html = '<pre>';
            
            if (typeof localStorage !== 'undefined') {
                const relevantKeys = [
                    'navigationHistory',
                    'ultimaSesionActiva',
                    'ultimoUsuarioActivo',
                    'ultimaPaginaConSesion',
                    'inteletgroup_ultima_visita'
                ];
                
                relevantKeys.forEach(key => {
                    const value = localStorage.getItem(key);
                    if (value) {
                        html += key + ': ' + value + '\n';
                    }
                });
                
                html += '\nTotal items en localStorage: ' + localStorage.length;
            } else {
                html += 'localStorage no disponible';
            }
            
            html += '</pre>';
            localStorageDiv.innerHTML = html;
        });
    </script>
</body>
</html>