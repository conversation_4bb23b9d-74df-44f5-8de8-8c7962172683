<?php
// Script para simular el problema de race condition que reportaste
echo "=== SIMULACIÓN PROBLEMA RACE CONDITION ===\n";

// Configuración
$baseUrl = 'https://www.gestarservicios.cl/intranet/dist/';
$username = '11111111-1';
$password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';

// Inicializar cURL con cookies
$cookieFile = tempnam(sys_get_temp_dir(), 'cookies');

function makeCurlRequest($url, $postData = null, $cookieFile = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    if ($cookieFile) {
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
    }
    
    if ($postData !== null) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json',
            'X-Requested-With: XMLHttpRequest'
        ]);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'response' => $response,
        'httpCode' => $httpCode,
        'error' => $error
    ];
}

// Función para verificar si un prospecto existe en la base de datos
function checkProspectInDB($rut) {
    $host = '*************';
    $port = 3306;
    $dbname = 'gestarse_experian';
    $username = 'gestarse_ncornejo7_experian';
    $password = 'N1c0l7as17';
    
    try {
        $conexion = new mysqli($host, $username, $password, $dbname, $port);
        
        if ($conexion->connect_error) {
            return "Error de conexión: " . $conexion->connect_error;
        }
        
        $stmt = $conexion->prepare("SELECT id, fecha_registro FROM tb_inteletgroup_prospectos WHERE rut_cliente = ? ORDER BY fecha_registro DESC LIMIT 1");
        $stmt->bind_param("s", $rut);
        $stmt->execute();
        $stmt->store_result();
        
        if ($stmt->num_rows > 0) {
            $id = null;
            $fecha = null;
            $stmt->bind_result($id, $fecha);
            $stmt->fetch();
            $stmt->close();
            $conexion->close();
            return ['exists' => true, 'id' => $id, 'fecha' => $fecha];
        } else {
            $stmt->close();
            $conexion->close();
            return ['exists' => false];
        }
    } catch (Exception $e) {
        return "Error: " . $e->getMessage();
    }
}

// Paso 1: Login
echo "1. Realizando login...\n";
$loginData = [
    'rut' => $username,
    'clave' => $password
];

$loginResult = makeCurlRequest($baseUrl . 'ControllerGestar.php', $loginData, $cookieFile);
if ($loginResult['httpCode'] === 200) {
    $loginJson = json_decode($loginResult['response'], true);
    if ($loginJson && isset($loginJson['success']) && $loginJson['success']) {
        echo "   ✅ Login exitoso\n";
    } else {
        echo "   ❌ Login fallido\n";
        exit(1);
    }
} else {
    echo "   ❌ Error en login\n";
    exit(1);
}

// Paso 2: Crear prospecto y verificar el comportamiento
echo "\n2. Creando prospecto y verificando comportamiento...\n";
$uniqueRut = rand(10000000, 99999999) . '-' . rand(0, 9);

$prospectData = [
    'usuario_id' => '4',
    'rut_cliente' => $uniqueRut,
    'razon_social' => 'EMPRESA PRUEBA RACE CONDITION LTDA',
    'nombre_ejecutivo' => 'Ejecutivo Race',
    'rubro' => 'Servicios de prueba race condition',
    'direccion_comercial' => 'Dirección Race 123',
    'telefono_celular' => '*********',
    'email' => '<EMAIL>',
    'tipo_persona' => 'Jurídica',
    'numero_pos' => '54321',
    'tipo_cuenta' => 'Corriente',
    'numero_cuenta_bancaria' => '*********',
    'dias_atencion' => 'Lunes a Viernes',
    'horario_atencion' => '8:00 - 17:00',
    'contrata_boleta' => 'Factura',
    'competencia_actual' => 'Transbank'
];

echo "   RUT generado: $uniqueRut\n";

// Verificar que no existe antes
echo "   Verificando que el RUT no existe en BD antes...\n";
$beforeCheck = checkProspectInDB($uniqueRut);
if (is_array($beforeCheck) && !$beforeCheck['exists']) {
    echo "   ✅ RUT no existe en BD (como esperado)\n";
} else {
    echo "   ❌ RUT ya existe o error: " . print_r($beforeCheck, true) . "\n";
    exit(1);
}

// Enviar solicitud
echo "   Enviando solicitud...\n";
$prospectResult = makeCurlRequest($baseUrl . 'guardar_prospecto_inteletgroup.php', $prospectData, $cookieFile);
echo "   Código HTTP: {$prospectResult['httpCode']}\n";
echo "   Respuesta: {$prospectResult['response']}\n";

// Verificar en BD después de la solicitud
echo "   Verificando estado en BD después de la solicitud...\n";
$afterCheck = checkProspectInDB($uniqueRut);

$prospectJson = json_decode($prospectResult['response'], true);

if (is_array($afterCheck) && $afterCheck['exists']) {
    echo "   📊 PROSPECTO EXISTE EN BD - ID: {$afterCheck['id']}, Fecha: {$afterCheck['fecha']}\n";
    
    if ($prospectJson) {
        if ($prospectJson['success']) {
            echo "   ✅ COMPORTAMIENTO CORRECTO: Prospecto guardado Y respuesta exitosa\n";
            echo "   ✅ ID del prospecto: {$prospectJson['prospecto_id']}\n";
        } else {
            echo "   🚨 PROBLEMA DETECTADO: Prospecto guardado PERO respuesta de error\n";
            echo "   🚨 Mensaje de error: {$prospectJson['message']}\n";
            echo "   🚨 Este es exactamente el problema que reportaste!\n";
            
            if (isset($prospectJson['duplicate']) && $prospectJson['duplicate']) {
                echo "   🚨 El sistema detectó incorrectamente un duplicado\n";
            }
        }
    } else {
        echo "   ❌ Respuesta no es JSON válido\n";
    }
} else {
    echo "   ❌ PROSPECTO NO EXISTE EN BD\n";
    if ($prospectJson && $prospectJson['success']) {
        echo "   🚨 PROBLEMA: Respuesta exitosa PERO prospecto no guardado\n";
    } else {
        echo "   ✅ Comportamiento consistente: Error en respuesta y no guardado\n";
    }
}

// Limpiar
if (file_exists($cookieFile)) {
    unlink($cookieFile);
}

echo "\n=== ANÁLISIS COMPLETADO ===\n";
echo "Si ves '🚨 PROBLEMA DETECTADO', significa que el problema original aún existe.\n";
echo "Si ves '✅ COMPORTAMIENTO CORRECTO', significa que el problema fue solucionado.\n";
?>
