<?php
// Script para debugging detallado de errores en InteletGroup
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', 'logs/inteletgroup_debug.log');

// Función para logging detallado
function debug_log($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] [$level] $message" . PHP_EOL;
    file_put_contents('logs/inteletgroup_debug.log', $log_entry, FILE_APPEND | LOCK_EX);
}

// Función para capturar todos los errores
function error_handler($errno, $errstr, $errfile, $errline) {
    $error_types = [
        E_ERROR => 'ERROR',
        E_WARNING => 'WARNING', 
        E_PARSE => 'PARSE',
        E_NOTICE => 'NOTICE',
        E_CORE_ERROR => 'CORE_ERROR',
        E_CORE_WARNING => 'CORE_WARNING',
        E_COMPILE_ERROR => 'COMPILE_ERROR',
        E_COMPILE_WARNING => 'COMPILE_WARNING',
        E_USER_ERROR => 'USER_ERROR',
        E_USER_WARNING => 'USER_WARNING',
        E_USER_NOTICE => 'USER_NOTICE',
        E_STRICT => 'STRICT',
        E_RECOVERABLE_ERROR => 'RECOVERABLE_ERROR',
        E_DEPRECATED => 'DEPRECATED',
        E_USER_DEPRECATED => 'USER_DEPRECATED'
    ];
    
    $error_type = isset($error_types[$errno]) ? $error_types[$errno] : 'UNKNOWN';
    debug_log("PHP $error_type: $errstr in $errfile on line $errline", 'ERROR');
    
    // No detener la ejecución para warnings y notices
    if ($errno == E_ERROR || $errno == E_CORE_ERROR || $errno == E_COMPILE_ERROR || $errno == E_USER_ERROR) {
        exit(1);
    }
    return true;
}

// Función para capturar errores fatales
function fatal_error_handler() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
        debug_log("FATAL ERROR: {$error['message']} in {$error['file']} on line {$error['line']}", 'FATAL');
    }
}

// Registrar manejadores de errores
set_error_handler('error_handler');
register_shutdown_function('fatal_error_handler');

debug_log("=== INICIO DEBUG INTELETGROUP ===");
debug_log("PHP Version: " . phpversion());
debug_log("Server: " . $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown');
debug_log("Request Method: " . $_SERVER['REQUEST_METHOD'] ?? 'Unknown');
debug_log("Request URI: " . $_SERVER['REQUEST_URI'] ?? 'Unknown');

// Verificar si es una solicitud POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    debug_log("POST Data received: " . json_encode($_POST));
    debug_log("FILES Data received: " . json_encode($_FILES));
}

// Incluir el archivo original con manejo de errores
try {
    debug_log("Incluyendo guardar_prospecto_inteletgroup.php");
    include 'guardar_prospecto_inteletgroup.php';
    debug_log("Archivo incluido exitosamente");
} catch (Exception $e) {
    debug_log("Exception caught: " . $e->getMessage(), 'ERROR');
    debug_log("Stack trace: " . $e->getTraceAsString(), 'ERROR');
    
    // Responder con error JSON
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Error interno del servidor: ' . $e->getMessage(),
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
} catch (Error $e) {
    debug_log("Fatal Error caught: " . $e->getMessage(), 'FATAL');
    debug_log("Stack trace: " . $e->getTraceAsString(), 'FATAL');
    
    // Responder con error JSON
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Error fatal del servidor: ' . $e->getMessage(),
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
}

debug_log("=== FIN DEBUG INTELETGROUP ===");
?>
