<?php
// Script para probar el mapeo correcto de documentos
echo "=== PRUEBA MAPEO DE DOCUMENTOS ===\n";

// Configuración
$baseUrl = 'https://www.gestarservicios.cl/intranet/dist/';
$username = '11111111-1';
$password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';

// Inicializar cURL con cookies
$cookieFile = tempnam(sys_get_temp_dir(), 'cookies');

function makeCurlRequestWithFiles($url, $postData, $files, $cookieFile = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    if ($cookieFile) {
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
    }
    
    curl_setopt($ch, CURLOPT_POST, true);
    
    // Combinar datos POST y archivos
    $allData = array_merge($postData, $files);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $allData);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'response' => $response,
        'httpCode' => $httpCode,
        'error' => $error
    ];
}

function makeCurlRequest($url, $postData = null, $cookieFile = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    if ($cookieFile) {
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
    }
    
    if ($postData !== null) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json',
            'X-Requested-With: XMLHttpRequest'
        ]);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'response' => $response,
        'httpCode' => $httpCode,
        'error' => $error
    ];
}

// Paso 1: Login
echo "1. Realizando login...\n";
$loginData = [
    'rut' => $username,
    'clave' => $password
];

$loginResult = makeCurlRequest($baseUrl . 'ControllerGestar.php', $loginData, $cookieFile);
if ($loginResult['httpCode'] === 200) {
    $loginJson = json_decode($loginResult['response'], true);
    if ($loginJson && isset($loginJson['success']) && $loginJson['success']) {
        echo "   ✅ Login exitoso\n";
    } else {
        echo "   ❌ Login fallido\n";
        exit(1);
    }
} else {
    echo "   ❌ Error en login\n";
    exit(1);
}

// Paso 2: Crear archivos de prueba
echo "\n2. Creando archivos de prueba...\n";
$testFiles = [];

// Crear archivos de prueba simples
$fileContents = [
    'doc_PN_CEDULA_FRONTAL_0' => 'Contenido de cédula frontal - ' . date('Y-m-d H:i:s'),
    'doc_PN_CEDULA_TRASERA_0' => 'Contenido de cédula trasera - ' . date('Y-m-d H:i:s'),
    'doc_AMBOS_PODER_NOTARIAL_0' => 'Contenido de poder notarial - ' . date('Y-m-d H:i:s')
];

foreach ($fileContents as $fieldName => $content) {
    $filePath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . $fieldName . '.txt';
    file_put_contents($filePath, $content);
    $testFiles[$fieldName] = new CURLFile($filePath, 'text/plain', $fieldName . '.txt');
    echo "   ✅ Creado: $fieldName.txt\n";
}

// Paso 3: Crear prospecto con documentos
echo "\n3. Creando prospecto con documentos...\n";
$uniqueRut = rand(10000000, 99999999) . '-' . rand(0, 9);

$prospectData = [
    'usuario_id' => '4',
    'rut_cliente' => $uniqueRut,
    'razon_social' => 'EMPRESA PRUEBA DOCUMENTOS LTDA',
    'nombre_ejecutivo' => 'Ejecutivo Documentos',
    'rubro' => 'Servicios de prueba documentos',
    'direccion_comercial' => 'Dirección Documentos 123',
    'telefono_celular' => '*********',
    'email' => '<EMAIL>',
    'tipo_persona' => 'Natural',
    'numero_pos' => '54321',
    'tipo_cuenta' => 'Corriente',
    'numero_cuenta_bancaria' => '*********',
    'dias_atencion' => 'Lunes a Viernes',
    'horario_atencion' => '8:00 - 17:00',
    'contrata_boleta' => 'Factura',
    'competencia_actual' => 'Transbank'
];

echo "   RUT: $uniqueRut\n";

$prospectResult = makeCurlRequestWithFiles($baseUrl . 'guardar_prospecto_inteletgroup.php', $prospectData, $testFiles, $cookieFile);
echo "   Código HTTP: {$prospectResult['httpCode']}\n";
echo "   Respuesta: {$prospectResult['response']}\n";

$prospectJson = json_decode($prospectResult['response'], true);
if ($prospectJson && $prospectJson['success']) {
    $prospecto_id = $prospectJson['prospecto_id'];
    echo "   ✅ PROSPECTO CON DOCUMENTOS CREADO - ID: $prospecto_id\n";
    
    // Mostrar documentos procesados
    if (isset($prospectJson['documentos']) && !empty($prospectJson['documentos'])) {
        echo "   📄 Documentos procesados:\n";
        foreach ($prospectJson['documentos'] as $doc) {
            echo "     - Campo: {$doc['campo']}\n";
            echo "       Archivo: {$doc['archivo']}\n";
            echo "       Ruta: {$doc['ruta']}\n";
        }
    }
} else {
    echo "   ❌ Error creando prospecto con documentos\n";
    if ($prospectJson) {
        echo "   Error: {$prospectJson['message']}\n";
    }
}

// Limpiar archivos temporales
echo "\n4. Limpiando archivos temporales...\n";
foreach ($fileContents as $fieldName => $content) {
    $filePath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . $fieldName . '.txt';
    if (file_exists($filePath)) {
        unlink($filePath);
        echo "   ✅ Eliminado: $fieldName.txt\n";
    }
}

if (file_exists($cookieFile)) {
    unlink($cookieFile);
}

echo "\n=== FIN PRUEBA MAPEO DOCUMENTOS ===\n";
?>
