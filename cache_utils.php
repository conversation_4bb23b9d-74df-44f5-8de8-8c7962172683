<?php
/**
 * Utilidades para manejo de cache y versiones de recursos
 */

/**
 * Detecta si el usuario está en un dispositivo móvil
 */
function is_mobile() {
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    return preg_match('/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i', $user_agent) || preg_match('/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i', substr($user_agent, 0, 4));
}

/**
 * Aplica los headers necesarios para evitar el cacheo del contenido
 */
function no_cache_headers() {
    if (headers_sent()) {
        return;
    }
    
    // Detectar si es móvil
    $is_mobile = is_mobile();
    
    if ($is_mobile) {
        // Headers más agresivos para móviles
        // Los navegadores móviles son más persistentes con el caché
        header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0, s-maxage=0, proxy-revalidate, no-transform');
        header('Pragma: no-cache');
        header('Expires: 0');
        header('Vary: *');
        
        // Headers adicionales para móviles
        header('X-Accel-Expires: 0');
        header('X-Cache-Enabled: False');
        header('X-Powered-By: InteletGroup/1.0');
    } else {
        // Headers para desktop - Estrategia TQW
        // Permite navegación fluida pero con validación
        header('Cache-Control: private, must-revalidate');
        header('Pragma: private');
    }
    
    // Establecer fecha de última modificación
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
    
    // ETag basado en contenido para validación inteligente
    if (isset($_SESSION['last_update'])) {
        header('ETag: "' . md5($_SESSION['last_update'] . ($is_mobile ? '_mobile' : '_desktop')) . '"');
    }
}

/**
 * Genera meta tags para evitar el cacheo
 */
function no_cache_meta() {
    $is_mobile = is_mobile();
    
    if ($is_mobile) {
        // Meta tags agresivos para móviles
        return '
    <!-- Estrategia de caché para móviles -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="HandheldFriendly" content="true">
    <meta name="MobileOptimized" content="320">
    <meta name="robots" content="noarchive">
    ';
    } else {
        // Meta tags estándar para desktop
        return '
    <!-- Estrategia de caché controlado TQW -->
    <meta http-equiv="Cache-Control" content="private, must-revalidate">
    <meta http-equiv="Pragma" content="private">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    ';
    }
}

/**
 * Añade un parámetro de versión a las URLs para forzar la recarga
 * de recursos estáticos cuando cambian
 */
function version_url($path) {
    $fullPath = __DIR__ . '/' . $path;
    $version = '';

    // Detectar entorno de desarrollo (localhost o 127.0.0.1)
    $is_dev = (isset($_SERVER['HTTP_HOST']) && (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false));

    if ($is_dev) {
        // En desarrollo, usar timestamp para forzar recarga frecuente
        $version = time();
    } else {
        // En producción, usar fecha de modificación del archivo
        $version = file_exists($fullPath) ? filemtime($fullPath) : time();
    }
    
    return $path . '?v=' . $version;
}

/**
 * Establece un valor para la cookie de sesión con configuración segura
 */
function set_secure_session_cookie() {
    // Obtener el nombre actual de la sesión
    $session_name = session_name();
    
    // Configurar la cookie para ser más segura
    $cookie_params = session_get_cookie_params();
    
    setcookie(
        $session_name,
        session_id(),
        [
            'expires' => time() + $cookie_params['lifetime'],
            'path' => $cookie_params['path'],
            'domain' => $cookie_params['domain'],
            'secure' => true,                  // Solo sobre HTTPS
            'httponly' => true,                // No accesible via JavaScript
            'samesite' => 'Lax'                // Restringir cookies cross-site
        ]
    );
}

/**
 * Regenera el ID de sesión para prevenir ataques de fijación de sesión
 */
function regenerate_session() {
    // Regenerar ID de sesión y mantener datos
    if (!headers_sent()) {
        session_regenerate_id(true);
        set_secure_session_cookie();
    }
}

/**
 * Registra información de depuración en un archivo de log
 */
function debug_log($message, $data = []) {
    $log_file = __DIR__ . '/logs/debug.log';
    $log_dir = dirname($log_file);
    
    // Crear directorio de logs si no existe
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    // Formatear mensaje
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    $session_id = session_id() ?: 'No session';
    
    $log_entry = "[$timestamp] [$ip] [$session_id] $message";
    
    // Añadir datos extra si existen
    if (!empty($data)) {
        $log_entry .= " | Data: " . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    
    // Añadir info del user agent
    $log_entry .= " | UA: $user_agent";
    $log_entry .= PHP_EOL;
    
    // Escribir al archivo
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

function dev_no_cache_headers() {
    // Función mantenida por compatibilidad, pero ahora los headers se manejan en init.php
    if (defined('ENVIRONMENT') && ENVIRONMENT === 'development' && !headers_sent()) {
        header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
        header("Cache-Control: post-check=0, pre-check=0", false);
        header("Pragma: no-cache");
        header('Expires: 0');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
    }
}
