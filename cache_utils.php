<?php
/**
 * Utilidades para manejo de cache y versiones de recursos
 */

/**
 * Detecta si el usuario está en un dispositivo móvil
 */
function is_mobile() {
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    return preg_match('/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i', $user_agent) || preg_match('/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i', substr($user_agent, 0, 4));
}

/**
 * Aplica headers básicos para evitar caché - VERSIÓN SIMPLIFICADA
 * Evita bucles infinitos con headers mínimos pero efectivos
 */
function no_cache_headers() {
    if (headers_sent()) {
        return;
    }

    // Headers básicos y seguros para evitar caché
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');

    // Header personalizado para debugging
    header('X-No-Cache-Applied: ' . date('Y-m-d H:i:s'));
}

/**
 * Genera meta tags para evitar COMPLETAMENTE el cacheo
 * VERSIÓN SIN CACHÉ - ELIMINADO COMPLETAMENTE
 */
function no_cache_meta() {
    // Meta tags básicos y seguros para evitar caché
    return '
    <!-- ANTI-CACHÉ BÁSICO -->
    <meta http-equiv="Cache-Control" content="no-cache, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    ';
}

/**
 * Añade un parámetro de versión a las URLs para forzar la recarga SIEMPRE
 * VERSIÓN SIN CACHÉ - SIEMPRE TIMESTAMP ACTUAL + RANDOM
 */
function version_url($path) {
    // SIEMPRE usar timestamp actual + microsegundos + random para garantizar URLs únicas
    $version = time() . '.' . microtime(true) . '.' . rand(1000, 9999);

    // Agregar múltiples parámetros para evitar cualquier tipo de caché
    $separator = (strpos($path, '?') !== false) ? '&' : '?';

    return $path . $separator . 'v=' . $version . '&nocache=' . time() . '&rand=' . rand();
}

/**
 * Establece un valor para la cookie de sesión con configuración segura
 */
function set_secure_session_cookie() {
    // Obtener el nombre actual de la sesión
    $session_name = session_name();
    
    // Configurar la cookie para ser más segura
    $cookie_params = session_get_cookie_params();
    
    setcookie(
        $session_name,
        session_id(),
        [
            'expires' => time() + $cookie_params['lifetime'],
            'path' => $cookie_params['path'],
            'domain' => $cookie_params['domain'],
            'secure' => true,                  // Solo sobre HTTPS
            'httponly' => true,                // No accesible via JavaScript
            'samesite' => 'Lax'                // Restringir cookies cross-site
        ]
    );
}

/**
 * Regenera el ID de sesión para prevenir ataques de fijación de sesión
 */
function regenerate_session() {
    // Regenerar ID de sesión y mantener datos
    if (!headers_sent()) {
        session_regenerate_id(true);
        set_secure_session_cookie();
    }
}

/**
 * Registra información de depuración en un archivo de log
 */
function debug_log($message, $data = []) {
    $log_file = __DIR__ . '/logs/debug.log';
    $log_dir = dirname($log_file);
    
    // Crear directorio de logs si no existe
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    // Formatear mensaje
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    $session_id = session_id() ?: 'No session';
    
    $log_entry = "[$timestamp] [$ip] [$session_id] $message";
    
    // Añadir datos extra si existen
    if (!empty($data)) {
        $log_entry .= " | Data: " . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    
    // Añadir info del user agent
    $log_entry .= " | UA: $user_agent";
    $log_entry .= PHP_EOL;
    
    // Escribir al archivo
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

function dev_no_cache_headers() {
    // Función básica para evitar caché en desarrollo
    if (!headers_sent()) {
        // Headers básicos y seguros
        header("Cache-Control: no-cache, must-revalidate");
        header("Pragma: no-cache");
        header('Expires: 0');
        header('X-Dev-No-Cache: ' . date('Y-m-d H:i:s'));
    }
}

/**
 * Función para limpiar caché - VERSIÓN SIMPLIFICADA
 * Evita bucles infinitos con limpieza mínima
 */
function clear_all_cache() {
    // Solo limpiar OPcache si es absolutamente necesario
    // Comentado para evitar bucles infinitos
    /*
    if (function_exists('opcache_invalidate') && ini_get('opcache.enable')) {
        opcache_invalidate($_SERVER['SCRIPT_FILENAME'], true);
    }
    */

    // Aplicar headers anti-caché
    no_cache_headers();

    // Limpiar cualquier buffer de salida
    if (ob_get_level()) {
        ob_clean();
    }
}

/**
 * JavaScript para limpiar caché del navegador
 */
function browser_cache_clear_js() {
    return '
<script>
// Limpiar caché del navegador de forma agresiva
(function() {
    // Limpiar localStorage
    if (typeof(Storage) !== "undefined") {
        localStorage.clear();
        sessionStorage.clear();
    }

    // Forzar recarga sin caché en el próximo refresh
    if (window.performance && window.performance.navigation.type === 1) {
        // Es un refresh, forzar recarga completa
        window.location.reload(true);
    }

    // Prevenir caché en AJAX
    if (typeof $ !== "undefined") {
        $.ajaxSetup({
            cache: false,
            headers: {
                "Cache-Control": "no-cache, no-store, must-revalidate",
                "Pragma": "no-cache",
                "Expires": "0"
            }
        });
    }

    // Agregar timestamp a todos los enlaces para evitar caché
    document.addEventListener("DOMContentLoaded", function() {
        var links = document.querySelectorAll("a[href]");
        links.forEach(function(link) {
            var href = link.getAttribute("href");
            if (href && !href.startsWith("#") && !href.startsWith("javascript:") && !href.startsWith("mailto:")) {
                var separator = href.indexOf("?") !== -1 ? "&" : "?";
                link.setAttribute("href", href + separator + "nocache=" + Date.now() + "&rand=" + Math.random());
            }
        });
    });
})();
</script>';
}
