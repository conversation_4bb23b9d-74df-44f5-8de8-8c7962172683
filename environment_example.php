<?php
/**
 * Ejemplo de uso del Sistema de Configuración Multi-Entorno
 * 
 * Este archivo muestra cómo usar las nuevas funciones y configuraciones
 * para manejar URLs y rutas de forma consistente entre entornos.
 */

// Incluir el sistema de inicialización
require_once 'init.php';

// Obtener configuración
$config = EnvironmentConfig::getInstance();
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema Multi-Entorno - Ejemplo</title>
    
    <!-- CSS usando el helper -->
    <link rel="stylesheet" href="<?php echo css_url('bootstrap.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo css_url('dashboard.css'); ?>">
    
    <style>
        .info-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .code-block {
            background: #f1f3f4;
            border-left: 4px solid #007bff;
            padding: 1rem;
            margin: 0.5rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .status-dev { background-color: #d4edda; color: #155724; }
        .status-prod { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Sistema de Configuración Multi-Entorno</h1>
        
        <!-- Estado del Entorno -->
        <div class="info-card">
            <h3>Estado Actual del Entorno</h3>
            <p>
                <strong>Entorno:</strong> 
                <span class="status-badge <?php echo $config->isDevelopment() ? 'status-dev' : 'status-prod'; ?>">
                    <?php echo strtoupper($config->getEnvironment()); ?>
                </span>
            </p>
            <p><strong>URL Base:</strong> <code><?php echo $config->getBaseUrl(); ?></code></p>
            <p><strong>Debug Habilitado:</strong> <?php echo $config->get('debug') ? 'Sí' : 'No'; ?></p>
            <p><strong>SSL Requerido:</strong> <?php echo $config->get('ssl_required') ? 'Sí' : 'No'; ?></p>
        </div>

        <!-- Ejemplos de URLs -->
        <div class="info-card">
            <h3>Ejemplos de Generación de URLs</h3>
            
            <h5>URLs Básicas:</h5>
            <div class="code-block">
                base_url() → <?php echo base_url(); ?><br>
                base_url('login.php') → <?php echo base_url('login.php'); ?><br>
                get_url('form_experian2.php') → <?php echo get_url('form_experian2.php'); ?>
            </div>

            <h5>URLs de Recursos:</h5>
            <div class="code-block">
                css_url('bootstrap.min.css') → <?php echo css_url('bootstrap.min.css'); ?><br>
                js_url('jquery-3.7.1.min.js') → <?php echo js_url('jquery-3.7.1.min.js'); ?><br>
                img_url('logoGestar.png') → <?php echo img_url('logoGestar.png'); ?>
            </div>

            <h5>URLs de API:</h5>
            <div class="code-block">
                api_url('obtener_prospectos.php') → <?php echo api_url('obtener_prospectos.php'); ?><br>
                api_url('exportar_clientes.php') → <?php echo api_url('exportar_clientes.php'); ?>
            </div>
        </div>

        <!-- Ejemplos de Código -->
        <div class="info-card">
            <h3>Ejemplos de Código</h3>
            
            <h5>1. Redirección Segura:</h5>
            <div class="code-block">
// Antes (problemático):<br>
header('Location: http://localhost/intranet/dist/login.php');<br><br>

// Ahora (correcto):<br>
redirect_to('login.php');<br>
// o<br>
redirect_to_login('Sesión expirada');
            </div>

            <h5>2. Inclusión de CSS/JS:</h5>
            <div class="code-block">
&lt;!-- Antes (problemático) --&gt;<br>
&lt;link rel="stylesheet" href="css/bootstrap.min.css"&gt;<br><br>

&lt;!-- Ahora (correcto) --&gt;<br>
&lt;link rel="stylesheet" href="&lt;?php echo css_url('bootstrap.min.css'); ?&gt;"&gt;
            </div>

            <h5>3. Enlaces en HTML:</h5>
            <div class="code-block">
&lt;!-- Antes (problemático) --&gt;<br>
&lt;a href="form_experian2.php"&gt;Formulario&lt;/a&gt;<br><br>

&lt;!-- Ahora (correcto) --&gt;<br>
&lt;a href="&lt;?php echo base_url('form_experian2.php'); ?&gt;"&gt;Formulario&lt;/a&gt;
            </div>

            <h5>4. AJAX/Fetch URLs:</h5>
            <div class="code-block">
// Antes (problemático):<br>
fetch('endpoints/obtener_prospectos.php')<br><br>

// Ahora (correcto):<br>
fetch('&lt;?php echo api_url('obtener_prospectos.php'); ?&gt;')
            </div>
        </div>

        <!-- Configuración Avanzada -->
        <div class="info-card">
            <h3>Configuración Avanzada</h3>
            
            <h5>Verificar Entorno:</h5>
            <div class="code-block">
if (is_development()) {<br>
&nbsp;&nbsp;&nbsp;&nbsp;// Código solo para desarrollo<br>
&nbsp;&nbsp;&nbsp;&nbsp;error_log('Debug info');<br>
}<br><br>

if (is_production()) {<br>
&nbsp;&nbsp;&nbsp;&nbsp;// Código solo para producción<br>
&nbsp;&nbsp;&nbsp;&nbsp;// Optimizaciones, cache, etc.<br>
}
            </div>

            <h5>Obtener Configuración:</h5>
            <div class="code-block">
$config = env_config();<br>
$debugMode = $config->get('debug', false);<br>
$baseUrl = $config->getBaseUrl();<br>
$environment = $config->getEnvironment();
            </div>
        </div>

        <!-- Migración -->
        <div class="info-card">
            <h3>Guía de Migración</h3>
            <p>Para migrar código existente al nuevo sistema:</p>
            
            <ol>
                <li><strong>Incluir init.php</strong> al inicio de cada archivo PHP</li>
                <li><strong>Reemplazar URLs hardcodeadas</strong> con las funciones helper</li>
                <li><strong>Usar base_url()</strong> para todas las redirecciones</li>
                <li><strong>Usar css_url() y js_url()</strong> para recursos</li>
                <li><strong>Usar api_url()</strong> para endpoints</li>
                <li><strong>Probar en ambos entornos</strong> para verificar funcionamiento</li>
            </ol>
        </div>

        <!-- Botones de Prueba -->
        <div class="info-card">
            <h3>Pruebas de Funcionalidad</h3>
            <div class="row">
                <div class="col-md-6">
                    <a href="<?php echo base_url('login.php'); ?>" class="btn btn-primary">Ir a Login</a>
                    <a href="<?php echo base_url('form_experian2.php'); ?>" class="btn btn-secondary">Formulario Experian</a>
                </div>
                <div class="col-md-6">
                    <button onclick="testAjax()" class="btn btn-info">Probar AJAX</button>
                    <button onclick="showCurrentUrl()" class="btn btn-warning">URL Actual</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript usando helpers -->
    <script src="<?php echo js_url('jquery-3.7.1.min.js'); ?>"></script>
    <script src="<?php echo js_url('bootstrap.bundle.min.js'); ?>"></script>
    
    <script>
        // Configuración JavaScript usando PHP
        const CONFIG = {
            baseUrl: '<?php echo $config->getBaseUrl(); ?>',
            environment: '<?php echo $config->getEnvironment(); ?>',
            isDevelopment: <?php echo $config->isDevelopment() ? 'true' : 'false'; ?>
        };

        function testAjax() {
            fetch('<?php echo api_url('test_endpoint.php'); ?>')
                .then(response => response.json())
                .then(data => {
                    alert('AJAX Test: ' + JSON.stringify(data));
                })
                .catch(error => {
                    alert('Error: ' + error.message);
                });
        }

        function showCurrentUrl() {
            alert('URL Actual: ' + window.location.href);
        }

        // Log de configuración en desarrollo
        if (CONFIG.isDevelopment) {
            console.log('Configuración del Sistema:', CONFIG);
        }
    </script>
</body>
</html>
