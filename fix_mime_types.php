<?php
// Script para corregir los tipos MIME en documentos existentes
require_once 'con_db.php';

echo "<h2>Corrección de tipos MIME en documentos</h2>";

// Mapeo de extensiones a tipos MIME
$extension_to_mime = [
    'pdf' => 'application/pdf',
    'doc' => 'application/msword',
    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'xls' => 'application/vnd.ms-excel',
    'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'txt' => 'text/plain',
    'csv' => 'text/csv',
    'rtf' => 'application/rtf',
    'zip' => 'application/zip'
];

// Obtener documentos que necesitan actualización
$sql = "SELECT id, tipo_archivo, nombre_archivo FROM tb_inteletgroup_documentos WHERE tipo_archivo NOT LIKE '%/%'";
$result = $mysqli->query($sql);

if (!$result) {
    die("Error en consulta: " . $mysqli->error);
}

$count = 0;
echo "<p>Documentos a actualizar: " . $result->num_rows . "</p>";

while ($row = $result->fetch_assoc()) {
    $id = $row['id'];
    $tipo_actual = $row['tipo_archivo'];
    $nombre_archivo = $row['nombre_archivo'];
    
    // Obtener extensión del nombre del archivo
    $ext = strtolower(pathinfo($nombre_archivo, PATHINFO_EXTENSION));
    
    // Determinar tipo MIME
    $nuevo_tipo = isset($extension_to_mime[$ext]) ? $extension_to_mime[$ext] : $tipo_actual;
    
    if ($nuevo_tipo != $tipo_actual) {
        $update_sql = "UPDATE tb_inteletgroup_documentos SET tipo_archivo = ? WHERE id = ?";
        $stmt = $mysqli->prepare($update_sql);
        $stmt->bind_param("si", $nuevo_tipo, $id);
        
        if ($stmt->execute()) {
            echo "<p>✓ Actualizado documento ID $id: '$tipo_actual' → '$nuevo_tipo'</p>";
            $count++;
        } else {
            echo "<p>✗ Error actualizando documento ID $id: " . $stmt->error . "</p>";
        }
        $stmt->close();
    }
}

echo "<h3>Resumen:</h3>";
echo "<p>Documentos actualizados: $count</p>";

$mysqli->close();
?>