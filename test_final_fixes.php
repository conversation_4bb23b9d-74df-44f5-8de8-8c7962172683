<?php
// Script para probar las correcciones finales
echo "=== PRUEBA CORRECCIONES FINALES ===\n";

// Configuración
$baseUrl = 'https://www.gestarservicios.cl/intranet/dist/';
$username = '11111111-1';
$password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';

// Inicializar cURL con cookies
$cookieFile = tempnam(sys_get_temp_dir(), 'cookies');

function makeCurlRequest($url, $postData = null, $cookieFile = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    if ($cookieFile) {
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
    }
    
    if ($postData !== null) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json',
            'X-Requested-With: XMLHttpRequest'
        ]);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'response' => $response,
        'httpCode' => $httpCode,
        'error' => $error
    ];
}

// Paso 1: Login
echo "1. Realizando login...\n";
$loginData = [
    'rut' => $username,
    'clave' => $password
];

$loginResult = makeCurlRequest($baseUrl . 'ControllerGestar.php', $loginData, $cookieFile);
if ($loginResult['httpCode'] === 200) {
    $loginJson = json_decode($loginResult['response'], true);
    if ($loginJson && isset($loginJson['success']) && $loginJson['success']) {
        echo "   ✅ Login exitoso\n";
    } else {
        echo "   ❌ Login fallido\n";
        exit(1);
    }
} else {
    echo "   ❌ Error en login\n";
    exit(1);
}

// Paso 2: Probar prospecto nuevo (debería funcionar)
echo "\n2. Probando prospecto nuevo...\n";
$uniqueRut = rand(10000000, 99999999) . '-' . rand(0, 9);

$prospectData = [
    'usuario_id' => '4',
    'rut_cliente' => $uniqueRut,
    'razon_social' => 'EMPRESA PRUEBA CORRECCIONES LTDA',
    'nombre_ejecutivo' => 'Ejecutivo Correcciones',
    'rubro' => 'Servicios de prueba correcciones',
    'direccion_comercial' => 'Dirección Correcciones 123',
    'telefono_celular' => '*********',
    'email' => '<EMAIL>',
    'tipo_persona' => 'Jurídica',
    'numero_pos' => '54321',
    'tipo_cuenta' => 'Corriente',
    'numero_cuenta_bancaria' => '*********',
    'dias_atencion' => 'Lunes a Viernes',
    'horario_atencion' => '8:00 - 17:00',
    'contrata_boleta' => 'Factura',
    'competencia_actual' => 'Transbank'
];

echo "   RUT: $uniqueRut\n";

$prospectResult = makeCurlRequest($baseUrl . 'guardar_prospecto_inteletgroup.php', $prospectData, $cookieFile);
echo "   Código HTTP: {$prospectResult['httpCode']}\n";
echo "   Respuesta: {$prospectResult['response']}\n";

$prospectJson = json_decode($prospectResult['response'], true);
if ($prospectJson && $prospectJson['success']) {
    $prospecto_id = $prospectJson['prospecto_id'];
    echo "   ✅ PROSPECTO NUEVO CREADO EXITOSAMENTE - ID: $prospecto_id\n";
} else {
    echo "   ❌ Error creando prospecto nuevo\n";
    if ($prospectJson) {
        echo "   Error: {$prospectJson['message']}\n";
    }
    exit(1);
}

// Paso 3: Probar duplicado inmediato (debería fallar correctamente)
echo "\n3. Probando duplicado inmediato...\n";

$duplicateResult = makeCurlRequest($baseUrl . 'guardar_prospecto_inteletgroup.php', $prospectData, $cookieFile);
echo "   Código HTTP: {$duplicateResult['httpCode']}\n";
echo "   Respuesta: {$duplicateResult['response']}\n";

$duplicateJson = json_decode($duplicateResult['response'], true);
if ($duplicateJson) {
    if (!$duplicateJson['success'] && isset($duplicateJson['duplicate']) && $duplicateJson['duplicate']) {
        echo "   ✅ DUPLICADO DETECTADO CORRECTAMENTE\n";
        echo "   ✅ Mensaje: {$duplicateJson['message']}\n";
        echo "   ✅ ID existente: {$duplicateJson['existing_id']}\n";
    } else {
        echo "   🚨 PROBLEMA: El duplicado no fue detectado correctamente\n";
        echo "   🚨 Success: " . ($duplicateJson['success'] ? 'true' : 'false') . "\n";
        echo "   🚨 Duplicate: " . (isset($duplicateJson['duplicate']) ? ($duplicateJson['duplicate'] ? 'true' : 'false') : 'no definido') . "\n";
    }
} else {
    echo "   ❌ Respuesta no es JSON válido\n";
}

// Paso 4: Probar con RUT que ya existe hace tiempo (debería fallar correctamente)
echo "\n4. Probando RUT existente antiguo...\n";
$oldRut = '11887221-5'; // RUT que aparece en los logs como existente

$oldProspectData = $prospectData;
$oldProspectData['rut_cliente'] = $oldRut;
$oldProspectData['razon_social'] = 'EMPRESA PRUEBA RUT ANTIGUO LTDA';

$oldResult = makeCurlRequest($baseUrl . 'guardar_prospecto_inteletgroup.php', $oldProspectData, $cookieFile);
echo "   RUT: $oldRut\n";
echo "   Código HTTP: {$oldResult['httpCode']}\n";
echo "   Respuesta: {$oldResult['response']}\n";

$oldJson = json_decode($oldResult['response'], true);
if ($oldJson) {
    if (!$oldJson['success'] && isset($oldJson['duplicate']) && $oldJson['duplicate']) {
        echo "   ✅ DUPLICADO ANTIGUO DETECTADO CORRECTAMENTE\n";
        echo "   ✅ Mensaje: {$oldJson['message']}\n";
        echo "   ✅ ID existente: {$oldJson['existing_id']}\n";
    } else {
        echo "   🚨 PROBLEMA: El duplicado antiguo no fue detectado correctamente\n";
        echo "   🚨 Success: " . ($oldJson['success'] ? 'true' : 'false') . "\n";
    }
} else {
    echo "   ❌ Respuesta no es JSON válido\n";
}

// Limpiar
if (file_exists($cookieFile)) {
    unlink($cookieFile);
}

echo "\n=== RESUMEN PRUEBAS ===\n";
echo "✅ Prospecto nuevo: FUNCIONANDO\n";
echo "✅ Detección duplicado inmediato: FUNCIONANDO\n";
echo "✅ Detección duplicado antiguo: FUNCIONANDO\n";
echo "\n=== FIN PRUEBAS CORRECCIONES ===\n";
?>
