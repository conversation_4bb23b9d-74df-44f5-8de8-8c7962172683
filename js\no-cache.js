/**
 * Script básico para evitar caché - VERSIÓN SIMPLIFICADA
 * Evita bucles infinitos con funcionalidad mínima pero efectiva
 */

(function() {
    'use strict';

    console.log('🚫 Iniciando anti-caché básico');

    // 1. Configurar AJAX para no usar caché (solo si jQuery está disponible)
    if (typeof $ !== "undefined") {
        $.ajaxSetup({
            cache: false
        });
        console.log('✅ jQuery AJAX configurado sin caché');
    }

    // 2. Limpiar Cache Storage básico (sin agresividad)
    try {
        if ('caches' in window) {
            caches.keys().then(cacheNames => {
                if (cacheNames.length > 0) {
                    return Promise.all(
                        cacheNames.map(cacheName => {
                            console.log('🗑️ Eliminando cache:', cacheName);
                            return caches.delete(cacheName);
                        })
                    );
                }
            }).then(() => {
                console.log('✅ Cache Storage limpiado');
            }).catch(e => console.warn('⚠️ Error limpiando Cache Storage:', e));
        }
    } catch(e) {
        console.warn('⚠️ Error accediendo a Cache Storage:', e);
    }

    console.log('✅ Anti-caché básico completado');

})();
