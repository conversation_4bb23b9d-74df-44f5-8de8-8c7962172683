/**
 * Script ultra agresivo para eliminar TODO tipo de caché del navegador
 * Versión sin caché - Eliminar completamente
 */

(function() {
    'use strict';
    
    console.log('🚫 INICIANDO LIMPIEZA TOTAL DE CACHÉ');
    
    // 1. Limpiar localStorage y sessionStorage
    try {
        if (typeof(Storage) !== "undefined") {
            localStorage.clear();
            sessionStorage.clear();
            console.log('✅ localStorage y sessionStorage limpiados');
        }
    } catch(e) {
        console.warn('⚠️ Error limpiando storage:', e);
    }
    
    // 2. Limpiar IndexedDB
    try {
        if ('indexedDB' in window) {
            indexedDB.databases().then(databases => {
                databases.forEach(db => {
                    indexedDB.deleteDatabase(db.name);
                });
                console.log('✅ IndexedDB limpiado');
            }).catch(e => console.warn('⚠️ Error limpiando IndexedDB:', e));
        }
    } catch(e) {
        console.warn('⚠️ Error accediendo a IndexedDB:', e);
    }
    
    // 3. Limpiar WebSQL (si está disponible)
    try {
        if ('openDatabase' in window) {
            // WebSQL está deprecado pero algunos navegadores aún lo soportan
            console.log('✅ WebSQL detectado (deprecado)');
        }
    } catch(e) {
        console.warn('⚠️ Error con WebSQL:', e);
    }
    
    // 4. Configurar AJAX para no usar caché
    if (typeof $ !== "undefined") {
        $.ajaxSetup({
            cache: false,
            headers: {
                "Cache-Control": "no-cache, no-store, must-revalidate",
                "Pragma": "no-cache",
                "Expires": "0"
            }
        });
        console.log('✅ jQuery AJAX configurado sin caché');
    }
    
    // 5. Configurar fetch para no usar caché
    if ('fetch' in window) {
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            if (args[1]) {
                args[1].cache = 'no-store';
                args[1].headers = {
                    ...args[1].headers,
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                };
            } else {
                args[1] = {
                    cache: 'no-store',
                    headers: {
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                };
            }
            return originalFetch.apply(this, args);
        };
        console.log('✅ Fetch API configurado sin caché');
    }
    
    // 6. Forzar recarga sin caché si es un refresh
    if (window.performance && window.performance.navigation.type === 1) {
        console.log('🔄 Refresh detectado, forzando recarga completa');
        setTimeout(() => {
            window.location.reload(true);
        }, 100);
    }
    
    // 7. Agregar timestamp a todos los enlaces cuando el DOM esté listo
    function addTimestampToLinks() {
        const links = document.querySelectorAll("a[href]");
        links.forEach(function(link) {
            const href = link.getAttribute("href");
            if (href && 
                !href.startsWith("#") && 
                !href.startsWith("javascript:") && 
                !href.startsWith("mailto:") &&
                !href.startsWith("tel:") &&
                !href.includes("nocache=")) {
                
                const separator = href.indexOf("?") !== -1 ? "&" : "?";
                const newHref = href + separator + "nocache=" + Date.now() + "&rand=" + Math.random();
                link.setAttribute("href", newHref);
            }
        });
        console.log('✅ Timestamps agregados a ' + links.length + ' enlaces');
    }
    
    // 8. Agregar timestamp a formularios
    function addTimestampToForms() {
        const forms = document.querySelectorAll("form");
        forms.forEach(function(form) {
            // Agregar campo oculto con timestamp
            const timestampInput = document.createElement("input");
            timestampInput.type = "hidden";
            timestampInput.name = "nocache";
            timestampInput.value = Date.now();
            form.appendChild(timestampInput);
            
            const randomInput = document.createElement("input");
            randomInput.type = "hidden";
            randomInput.name = "rand";
            randomInput.value = Math.random();
            form.appendChild(randomInput);
        });
        console.log('✅ Timestamps agregados a ' + forms.length + ' formularios');
    }
    
    // 9. Limpiar caché de imágenes
    function clearImageCache() {
        const images = document.querySelectorAll("img[src]");
        images.forEach(function(img) {
            const src = img.getAttribute("src");
            if (src && !src.includes("nocache=")) {
                const separator = src.indexOf("?") !== -1 ? "&" : "?";
                img.setAttribute("src", src + separator + "nocache=" + Date.now());
            }
        });
        console.log('✅ Caché de imágenes limpiado para ' + images.length + ' imágenes');
    }
    
    // 10. Ejecutar cuando el DOM esté listo
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            addTimestampToLinks();
            addTimestampToForms();
            clearImageCache();
        });
    } else {
        addTimestampToLinks();
        addTimestampToForms();
        clearImageCache();
    }
    
    // 11. Limpiar caché cada 30 segundos
    setInterval(function() {
        try {
            if (typeof(Storage) !== "undefined") {
                localStorage.clear();
                sessionStorage.clear();
            }
        } catch(e) {
            console.warn('⚠️ Error en limpieza periódica:', e);
        }
    }, 30000);
    
    // 12. Prevenir caché en eventos de navegación
    window.addEventListener('beforeunload', function() {
        try {
            if (typeof(Storage) !== "undefined") {
                localStorage.clear();
                sessionStorage.clear();
            }
        } catch(e) {
            console.warn('⚠️ Error en limpieza antes de salir:', e);
        }
    });
    
    // 13. Forzar recarga si se detecta caché
    if (document.documentElement.getAttribute('data-cached') === 'true') {
        console.log('🔄 Página cacheada detectada, forzando recarga');
        window.location.reload(true);
    }
    
    console.log('🚫 LIMPIEZA TOTAL DE CACHÉ COMPLETADA');
    
})();
