/* Estilos para el formulario de prospectos InteletGroup */

/* Variables de color mejoradas para mejor contraste y armonía */
:root {
    /* Colores principales - Paleta cohesiva */
    --primary-blue: #2699FB;
    --primary-dark: #1e3a8a;
    --primary-medium: #3b82f6;
    --primary-light: #60a5fa;

    /* Header con gradiente profesional */
    --header-gradient: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #2563eb 100%);
    --header-overlay: rgba(30, 58, 138, 0.95);

    /* Colores de estado */
    --success-color: #10b981;
    --success-light: #34d399;
    --info-color: #06b6d4;
    --info-light: #22d3ee;
    --warning-color: #f59e0b;
    --warning-light: #fbbf24;
    --secondary-color: #6b7280;
    --secondary-light: #9ca3af;

    /* Colores neutros */
    --light-color: #f8fafc;
    --light-gray: #f1f5f9;
    --medium-gray: #e2e8f0;
    --dark-color: #1f2937;
    --dark-medium: #374151;
    --body-bg: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

    /* Efectos y sombras */
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease-out;

    /* Colores específicos de tarjetas */
    --card-green-border: var(--success-color);
    --card-blue-border: var(--info-color);
    --card-yellow-border: var(--warning-color);
    --card-gray-border: var(--secondary-color);
}

/* Estilos base del body */
html {
    zoom: 80%; /* Works in most modern browsers */
}

/* Eliminar márgenes laterales y hacer que el contenedor ocupe todo el ancho */
body {
    margin: 0;
    padding: 0;
    background: var(--body-bg);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: var(--dark-color);
    line-height: 1.6;
}

.container {
    max-width: 100% !important;
    padding-left: 15px;
    padding-right: 15px;
    margin-left: 0;
    margin-right: 0;
    width: 100%;
}

.row {
    margin-left: 0;
    margin-right: 0;
}

/* Header profesional con gradiente mejorado */
.simple-header {
    background: var(--header-gradient);
    color: white;
    box-shadow: var(--box-shadow-lg);
    margin-bottom: 2rem;
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 0;
    backdrop-filter: blur(10px);
}

/* Overlay para mejorar contraste */
.simple-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--header-overlay);
    z-index: -1;
}

/* Contenedor principal del header */
.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
    position: relative;
    z-index: 1;
}

/* Logo y nombre de la empresa */
.brand-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Logo wrapper mejorado */
.logo-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: var(--transition);
}

.logo-wrapper:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: scale(1.05);
}

.logo-wrapper img {
    height: 28px;
    width: 28px;
    object-fit: contain;
    border-radius: 50%;
}

/* Fallback icon si no hay imagen */
.logo-wrapper::after {
    content: '🏢';
    font-size: 1.5rem;
    display: none;
}

.logo-wrapper img[style*="display: none"] + ::after {
    display: block;
}

/* Estilos para la información del sitio */
.site-info {
    display: flex;
    flex-direction: column;
}

.site-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    padding: 0;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: -0.025em;
}

.site-subtitle {
    font-size: 0.875rem;
    opacity: 0.9;
    margin: 0;
    padding: 0;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

/* Usuario y acciones */
.user-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Estilos para el nombre de usuario */
.user-info-container {
    text-align: right;
    line-height: 1.3;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-sm);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.user-name {
    font-size: 1rem;
    font-weight: 600;
    color: white;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-role {
    font-size: 0.8rem;
    opacity: 0.9;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-weight: 500;
}

/* Botón de logout mejorado */
.logout-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.75rem;
    font-size: 1rem;
    border-radius: 50%;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    text-decoration: none;
    backdrop-filter: blur(10px);
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.logout-btn:active {
    transform: scale(0.95);
}

/* Estilos para cards y componentes modernos */
.welcome-card {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-medium) 50%, var(--primary-light) 100%);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    overflow: hidden;
    position: relative;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

/* Tarjetas de funcionalidades mejoradas */
.feature-card {
    transition: var(--transition);
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, transparent, var(--primary-medium), transparent);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 2rem;
    text-align: center;
}

.feature-card .card-text {
    flex-grow: 1;
    margin-bottom: 1.5rem;
    color: var(--dark-medium);
    line-height: 1.6;
}

.feature-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--box-shadow-lg);
}

/* Estilos específicos para cada tipo de tarjeta */
.feature-card.green-card {
    border-left: 4px solid var(--success-color);
}

.feature-card.green-card:hover {
    border-left-color: var(--success-light);
}

.feature-card.blue-card {
    border-left: 4px solid var(--info-color);
}

.feature-card.blue-card:hover {
    border-left-color: var(--info-light);
}

.feature-card.yellow-card {
    border-left: 4px solid var(--warning-color);
}

.feature-card.yellow-card:hover {
    border-left-color: var(--warning-light);
}

.feature-card.gray-card {
    border-left: 4px solid var(--secondary-color);
}

.feature-card.gray-card:hover {
    border-left-color: var(--secondary-light);
}

/* Íconos de tarjetas mejorados */
.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-left: auto;
    margin-right: auto;
    transition: var(--transition);
    position: relative;
}

.green-card .feature-icon {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(52, 211, 153, 0.1));
    color: var(--success-color);
    border: 2px solid rgba(16, 185, 129, 0.2);
}

.blue-card .feature-icon {
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(34, 211, 238, 0.1));
    color: var(--info-color);
    border: 2px solid rgba(6, 182, 212, 0.2);
}

.yellow-card .feature-icon {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(251, 191, 36, 0.1));
    color: var(--warning-color);
    border: 2px solid rgba(245, 158, 11, 0.2);
}

.gray-card .feature-icon {
    background: linear-gradient(135deg, rgba(107, 114, 128, 0.1), rgba(156, 163, 175, 0.1));
    color: var(--secondary-color);
    border: 2px solid rgba(107, 114, 128, 0.2);
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--box-shadow);
}

/* Tarjetas próximamente */
.coming-soon {
    opacity: 0.8;
    position: relative;
    filter: grayscale(0.2);
}

.coming-soon::after {
    content: "Próximamente";
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(135deg, var(--warning-color), var(--warning-light));
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--box-shadow);
    z-index: 2;
}

.coming-soon .btn {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Estilos para notificaciones y botones modernos */
#inteletgroup-notifications-container {
    z-index: 9999;
    position: fixed;
    top: 70px;
    right: 20px;
    max-width: 350px;
}

#inteletgroup-notifications-container .alert {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: 6px;
    border-left: 4px solid;
    margin-bottom: 10px;
    padding: 15px;
    animation: slideInRight 0.3s ease-out;
}

#inteletgroup-notifications-container .alert-success {
    border-left-color: var(--success-color);
    background-color: rgba(40, 167, 69, 0.05);
}

#inteletgroup-notifications-container .alert-danger {
    border-left-color: var(--error-color);
    background-color: rgba(220, 53, 69, 0.05);
}

#inteletgroup-notifications-container .alert-info {
    border-left-color: var(--info-color);
    background-color: rgba(23, 162, 184, 0.05);
}

/* Estilos para mensajes en el modal */
#inteletgroup-message-container .alert {
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    animation: fadeInDown 0.5s;
    border-left: 4px solid;
    padding: 15px;
}

#inteletgroup-message-container .alert-success {
    border-left-color: var(--success-color);
    background-color: rgba(40, 167, 69, 0.05);
}

#inteletgroup-message-container .alert-danger {
    border-left-color: var(--error-color);
    background-color: rgba(220, 53, 69, 0.05);
}

#inteletgroup-message-container .alert-info {
    border-left-color: var(--info-color);
    background-color: rgba(23, 162, 184, 0.05);
}

/* Animaciones adicionales */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    20% {
        transform: scale(25, 25);
        opacity: 0.5;
    }
    100% {
        opacity: 0;
        transform: scale(40, 40);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive design para el header y cards */
@media (max-width: 768px) {
    .header-container {
        padding: 0.75rem 0;
    }

    .brand-section {
        gap: 0.75rem;
    }

    .site-title {
        font-size: 1.25rem;
    }

    .site-subtitle {
        font-size: 0.8rem;
    }

    .user-info-container {
        padding: 0.5rem 0.75rem;
    }

    .user-name {
        font-size: 0.9rem;
    }

    .user-role {
        font-size: 0.75rem;
    }

    .feature-card .card-body {
        padding: 1.5rem;
    }

    .feature-icon {
        width: 70px;
        height: 70px;
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .user-info-container {
        display: none;
    }

    .site-title {
        font-size: 1.1rem;
    }

    .site-subtitle {
        font-size: 0.75rem;
    }
}

/* Aplicar animaciones a los componentes */
.welcome-card {
    animation: fadeInUp 0.6s ease-out;
}

.feature-card {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }
.feature-card:nth-child(6) { animation-delay: 0.6s; }

.header-action-btn {
    background-color: var(--primary-dark);
    color: white !important;
    border: 1px solid var(--primary-medium);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: background-color 0.3s ease, border-color 0.3s ease;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
}

.header-action-btn:hover {
    background-color: var(--primary-blue);
    border-color: var(--primary-blue);
    color: white !important;
}

.header-action-btn .bi {
    margin-right: 0.5rem;
}

/* Modal personalizado */
#inteletGroupProspectModal .modal-dialog {
    max-width: 1200px;
}

#inteletGroupProspectModal .modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

#inteletGroupProspectModal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px 15px 0 0;
    padding: 1.5rem;
}

#inteletGroupProspectModal .modal-title {
    font-weight: 600;
    font-size: 1.25rem;
}

/* Secciones del formulario */
.form-section-title {
    color: #667eea;
    font-weight: 600;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

/* Campos del formulario */
.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-label i {
    color: #667eea;
}

.form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-control.is-valid, .form-select.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.88 1.88 3.75-3.75.94.94-4.69 4.69z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid, .form-select.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4m0-1.4-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Mensajes de validación */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #28a745;
}

/* Texto de ayuda */
.form-text {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Campos requeridos */
.text-danger {
    color: #dc3545 !important;
}

/* Botones */
.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
    border: none;
    color: #000;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e0a800 0%, #e67e00 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
}

/* Estados del botón de guardar */
.btn-loading {
    display: none;
}

.btn.loading .btn-text {
    display: none;
}

.btn.loading .btn-loading {
    display: inline-flex;
    align-items: center;
}

/* Mensajes de estado */
.alert {
    border: none;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

/* Animaciones */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.alert {
    animation: fadeIn 0.3s ease-out;
}

/* Responsive */
@media (max-width: 768px) {
    #inteletGroupProspectModal .modal-dialog {
        max-width: 95%;
        margin: 1rem auto;
    }
    
    #inteletGroupProspectModal .modal-header {
        padding: 1rem;
    }
    
    #inteletGroupProspectModal .modal-title {
        font-size: 1.1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* Estilos para campos de solo lectura */
.form-control[readonly] {
    background-color: #f8f9fa !important;
    border-color: #e9ecef;
    color: #6c757d;
}

/* Indicador de carga en el modal */
.modal-loading {
    position: relative;
}

.modal-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    z-index: 1050;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1051;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Estilos para archivos */
.file-upload-area {
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.file-upload-area:hover {
    border-color: #667eea;
    background-color: #f8f9fa;
}

.file-upload-area.dragover {
    border-color: #667eea;
    background-color: rgba(102, 126, 234, 0.1);
}

/* Lista de archivos seleccionados */
.selected-files {
    margin-top: 1rem;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 5px;
    margin-bottom: 0.5rem;
}

.file-item .file-info {
    display: flex;
    align-items: center;
}

.file-item .file-info i {
    margin-right: 0.5rem;
    color: #667eea;
}

.file-item .file-size {
    font-size: 0.8rem;
    color: #6c757d;
    margin-left: 0.5rem;
}

.file-item .remove-file {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 0.25rem;
}

.file-item .remove-file:hover {
    color: #c82333;
}
