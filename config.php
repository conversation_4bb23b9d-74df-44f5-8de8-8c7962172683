<?php
/**
 * Configuración Multi-Entorno para Intranet Gestar
 *
 * Este archivo detecta automáticamente el entorno (desarrollo/producción)
 * y configura todas las rutas y URLs base de forma dinámica.
 *
 * Compatible con PHP 7.3.33+
 *
 * <AUTHOR> Intranet Gestar
 * @version 2.0
 */

// Prevenir ejecución directa
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

/**
 * Clase para manejo de configuración multi-entorno
 */
class EnvironmentConfig {

    private static $instance = null;
    private $environment = null;
    private $config = array();

    /**
     * Singleton pattern para asegurar una sola instancia
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor privado para singleton
     */
    private function __construct() {
        $this->detectEnvironment();
        $this->loadConfiguration();
    }

    /**
     * Detecta automáticamente el entorno basado en múltiples factores
     */
    private function detectEnvironment() {
        // Factores para detectar entorno local
        $localIndicators = array(
            // Hosts locales
            isset($_SERVER['HTTP_HOST']) && (
                strpos($_SERVER['HTTP_HOST'], 'localhost') !== false ||
                strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false ||
                strpos($_SERVER['HTTP_HOST'], '::1') !== false ||
                preg_match('/^192\.168\./', $_SERVER['HTTP_HOST']) ||
                preg_match('/^10\./', $_SERVER['HTTP_HOST']) ||
                preg_match('/^172\.(1[6-9]|2[0-9]|3[01])\./', $_SERVER['HTTP_HOST'])
            ),
            // Puertos de desarrollo comunes
            isset($_SERVER['SERVER_PORT']) && in_array($_SERVER['SERVER_PORT'], array('3000', '8000', '8080', '8888')),
            // Variables de entorno de desarrollo
            isset($_SERVER['DEVELOPMENT']) || isset($_ENV['DEVELOPMENT']),
            // Detección por ruta del servidor
            isset($_SERVER['DOCUMENT_ROOT']) && (
                strpos($_SERVER['DOCUMENT_ROOT'], 'xampp') !== false ||
                strpos($_SERVER['DOCUMENT_ROOT'], 'wamp') !== false ||
                strpos($_SERVER['DOCUMENT_ROOT'], 'mamp') !== false ||
                strpos($_SERVER['DOCUMENT_ROOT'], 'laragon') !== false
            )
        );

        // Si cualquier indicador es verdadero, es desarrollo
        $this->environment = in_array(true, $localIndicators, true) ? 'development' : 'production';

        // Permitir override manual via variable de entorno
        if (isset($_ENV['FORCE_ENVIRONMENT'])) {
            $this->environment = $_ENV['FORCE_ENVIRONMENT'];
        }
    }

    /**
     * Carga la configuración específica del entorno
     */
    private function loadConfiguration() {
        // Configuración base común
        $baseConfig = array(
            'app_name' => 'Intranet Gestar',
            'app_version' => '2.0',
            'timezone' => 'America/Santiago',
            'charset' => 'UTF-8',
            'session_lifetime' => 28800, // 8 horas
        );

        // Configuración específica por entorno
        if ($this->environment === 'development') {
            $envConfig = array(
                'debug' => true,
                'error_reporting' => E_ALL,
                'display_errors' => true,
                'log_errors' => true,
                'base_url' => $this->buildBaseUrl(),
                'ssl_required' => false,
                'cache_enabled' => false,
                'minify_assets' => false,
            );
        } else {
            $envConfig = array(
                'debug' => false,
                'error_reporting' => E_ERROR | E_WARNING | E_PARSE,
                'display_errors' => false,
                'log_errors' => true,
                'base_url' => $this->buildBaseUrl(),
                'ssl_required' => true,
                'cache_enabled' => true,
                'minify_assets' => true,
            );
        }

        // Combinar configuraciones
        $this->config = array_merge($baseConfig, $envConfig);

        // Definir constantes globales para compatibilidad
        $this->defineConstants();
    }

    /**
     * Construye la URL base de forma inteligente
     */
    private function buildBaseUrl() {
        // Detectar protocolo
        $protocol = 'http://';
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') {
            $protocol = 'https://';
        } elseif (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
            $protocol = 'https://';
        } elseif (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443) {
            $protocol = 'https://';
        }

        // En desarrollo, forzar HTTP a menos que se especifique lo contrario
        if ($this->environment === 'development' && !isset($_SERVER['HTTPS'])) {
            $protocol = 'http://';
        }

        // Obtener host
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

        // Obtener ruta base
        $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
        $basePath = rtrim(dirname($scriptName), '/\\');

        // En desarrollo, incluir la ruta completa
        if ($this->environment === 'development') {
            $baseUrl = $protocol . $host . $basePath . '/';
        } else {
            // En producción, usar configuración más simple
            $baseUrl = $protocol . $host . '/intranet/dist/';
        }

        // Normalizar URL (eliminar dobles barras)
        $baseUrl = preg_replace('#/+#', '/', $baseUrl);
        $baseUrl = str_replace(':/', '://', $baseUrl);

        return $baseUrl;
    }

    /**
     * Define constantes globales para compatibilidad con código existente
     */
    private function defineConstants() {
        if (!defined('ENVIRONMENT')) {
            define('ENVIRONMENT', $this->environment);
        }

        if (!defined('BASE_URL')) {
            define('BASE_URL', $this->config['base_url']);
        }

        if (!defined('DEBUG_MODE')) {
            define('DEBUG_MODE', $this->config['debug']);
        }

        if (!defined('SSL_REQUIRED')) {
            define('SSL_REQUIRED', $this->config['ssl_required']);
        }
    }

    /**
     * Obtiene un valor de configuración
     */
    public function get($key, $default = null) {
        return isset($this->config[$key]) ? $this->config[$key] : $default;
    }

    /**
     * Obtiene el entorno actual
     */
    public function getEnvironment() {
        return $this->environment;
    }

    /**
     * Verifica si estamos en desarrollo
     */
    public function isDevelopment() {
        return $this->environment === 'development';
    }

    /**
     * Verifica si estamos en producción
     */
    public function isProduction() {
        return $this->environment === 'production';
    }

    /**
     * Obtiene la URL base
     */
    public function getBaseUrl() {
        return $this->config['base_url'];
    }

    /**
     * Construye una URL completa a partir de una ruta relativa
     */
    public function url($path = '') {
        $baseUrl = rtrim($this->config['base_url'], '/');
        $path = ltrim($path, '/');
        return $baseUrl . '/' . $path;
    }

    /**
     * Aplica configuración de PHP según el entorno
     */
    public function applyPhpConfig() {
        // Configurar zona horaria
        if (!empty($this->config['timezone'])) {
            date_default_timezone_set($this->config['timezone']);
        }

        // Configurar reporte de errores
        error_reporting($this->config['error_reporting']);
        ini_set('display_errors', $this->config['display_errors'] ? 1 : 0);
        ini_set('log_errors', $this->config['log_errors'] ? 1 : 0);

        // Configurar sesión SOLO si no ha sido iniciada
        if (session_status() === PHP_SESSION_NONE) {
            ini_set('session.gc_maxlifetime', $this->config['session_lifetime']);
            ini_set('session.cookie_lifetime', $this->config['session_lifetime']);
            ini_set('session.cookie_httponly', 1);
            ini_set('session.use_only_cookies', 1);

            // Configuraciones de seguridad
            if ($this->config['ssl_required']) {
                ini_set('session.cookie_secure', 1);
            }
        }
    }
}

// Inicializar configuración automáticamente
$envConfig = EnvironmentConfig::getInstance();
$envConfig->applyPhpConfig();

// Funciones helper globales para compatibilidad
if (!function_exists('env_config')) {
    function env_config($key = null, $default = null) {
        $config = EnvironmentConfig::getInstance();
        return $key === null ? $config : $config->get($key, $default);
    }
}

if (!function_exists('base_url')) {
    function base_url($path = '') {
        return EnvironmentConfig::getInstance()->url($path);
    }
}

if (!function_exists('is_development')) {
    function is_development() {
        return EnvironmentConfig::getInstance()->isDevelopment();
    }
}

if (!function_exists('is_production')) {
    function is_production() {
        return EnvironmentConfig::getInstance()->isProduction();
    }
}
