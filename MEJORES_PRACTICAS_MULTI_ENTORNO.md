# Mejores Prácticas para Desarrollo Multi-Entorno

## Resumen de la Solución Implementada

Se ha implementado un sistema robusto de configuración multi-entorno que resuelve los problemas de rutas y URLs entre desarrollo local y producción.

## Componentes del Sistema

### 1. `config.php` - Configuración Centralizada
- **Detección automática de entorno** basada en múltiples factores
- **Configuración específica** por entorno (desarrollo/producción)
- **Construcción inteligente de URLs** base
- **Constantes globales** para compatibilidad

### 2. `url_helpers.php` - Funciones Helper
- **Funciones de conveniencia** para generar URLs
- **Versionado automático** de CSS/JS en desarrollo
- **Funciones de seguridad** (CSRF, sanitización)
- **Utilidades de redirección** segura

### 3. `init.php` - Inicialización Global
- **Carga automática** del sistema de configuración
- **Headers apropiados** según el entorno
- **Configuración de PHP** automática
- **Logging** en desarrollo

## Funciones Principales

### Detección de Entorno
```php
// Automática - no requiere configuración manual
$config = EnvironmentConfig::getInstance();
echo $config->getEnvironment(); // 'development' o 'production'
```

### Generación de URLs
```php
// URL base
base_url()                          // http://localhost/intranet/dist/
base_url('login.php')              // http://localhost/intranet/dist/login.php

// Recursos con versionado automático
css_url('bootstrap.min.css')       // Incluye timestamp en desarrollo
js_url('jquery-3.7.1.min.js')     // Para evitar cache

// APIs y endpoints
api_url('obtener_prospectos.php')  // endpoints/obtener_prospectos.php
```

### Redirecciones Seguras
```php
// Redirección simple
redirect_to('dashboard.php');

// Redirección al login con error
redirect_to_login('Sesión expirada');

// Construcción de URLs con parámetros
build_url('search.php', ['q' => 'término', 'page' => 1]);
```

## Migración de Código Existente

### Paso 1: Incluir Inicialización
```php
// Al inicio de cada archivo PHP
require_once 'init.php';
```

### Paso 2: Reemplazar URLs Hardcodeadas
```php
// ❌ Antes (problemático)
header('Location: http://localhost/intranet/dist/login.php');
echo '<a href="form_experian2.php">Formulario</a>';

// ✅ Ahora (correcto)
redirect_to('login.php');
echo '<a href="' . base_url('form_experian2.php') . '">Formulario</a>';
```

### Paso 3: Actualizar Recursos
```php
// ❌ Antes
<link rel="stylesheet" href="css/bootstrap.min.css">
<script src="js/jquery-3.7.1.min.js"></script>

// ✅ Ahora
<link rel="stylesheet" href="<?php echo css_url('bootstrap.min.css'); ?>">
<script src="<?php echo js_url('jquery-3.7.1.min.js'); ?>"></script>
```

### Paso 4: Actualizar AJAX/Fetch
```javascript
// ❌ Antes
fetch('endpoints/obtener_prospectos.php')

// ✅ Ahora
fetch('<?php echo api_url('obtener_prospectos.php'); ?>')
```

## Configuración por Entorno

### Desarrollo Local
- **Detección automática**: localhost, 127.0.0.1, IPs privadas
- **Debug habilitado**: Errores visibles, logging detallado
- **Sin cache**: Recursos con timestamp para desarrollo ágil
- **HTTP permitido**: No requiere SSL
- **Headers de desarrollo**: Cache deshabilitado

### Producción
- **Detección automática**: Cualquier host que no sea local
- **Debug deshabilitado**: Errores ocultos, solo logging
- **Cache habilitado**: Optimización de recursos
- **HTTPS requerido**: Seguridad mejorada
- **Headers de seguridad**: HSTS, XSS Protection, etc.

## Ventajas del Sistema

### 1. **Cero Configuración Manual**
- No necesitas cambiar código al hacer deploy
- Detección automática del entorno
- Configuración apropiada automática

### 2. **Compatibilidad Total**
- Compatible con PHP 7.3.33+
- Funciona con código existente
- Migración gradual posible

### 3. **Seguridad Mejorada**
- Headers de seguridad automáticos
- Protección CSRF integrada
- Sanitización de URLs

### 4. **Desarrollo Ágil**
- Cache deshabilitado en desarrollo
- Versionado automático de recursos
- Logging detallado para debugging

### 5. **Mantenimiento Simplificado**
- Configuración centralizada
- Funciones helper reutilizables
- Código más limpio y mantenible

## Casos de Uso Comunes

### Formularios con CSRF
```php
<form method="POST" action="<?php echo base_url('guardar_prospecto.php'); ?>">
    <input type="hidden" name="csrf_token" value="<?php echo csrf_token(); ?>">
    <!-- campos del formulario -->
</form>
```

### Descargas de Documentos
```php
$downloadUrl = download_url($filename, 'prospecto');
echo '<a href="' . $downloadUrl . '">Descargar</a>';
```

### Verificación de Página Actual
```php
if (is_current_page('dashboard.php')) {
    echo '<li class="active">Dashboard</li>';
}
```

### Configuración JavaScript
```php
<script>
const CONFIG = {
    baseUrl: '<?php echo base_url(); ?>',
    environment: '<?php echo env_config()->getEnvironment(); ?>',
    isDevelopment: <?php echo is_development() ? 'true' : 'false'; ?>
};
</script>
```

## Solución a Problemas Específicos

### Problema Original: URLs Hardcodeadas
```php
// ❌ Problema
$redirectUrl = 'https://www.gestarservicios.cl/intranet/dist/dashboard.php';

// ✅ Solución
$redirectUrl = base_url('dashboard.php');
```

### Problema Original: Detección de Entorno
```php
// ❌ Problema
$isLocal = strpos($_SERVER['HTTP_HOST'], 'localhost') !== false;

// ✅ Solución
$isLocal = is_development();
```

### Problema Original: Rutas de Archivos
```php
// ❌ Problema
require_once '/home/<USER>/public_html/intranet/dist/config.php';

// ✅ Solución
require_once ABSPATH . 'config.php';
```

## Monitoreo y Debugging

### Logs en Desarrollo
```php
if (is_development()) {
    error_log("Debug: URL construida = " . base_url('test.php'));
}
```

### Información de Configuración
```php
// Ver configuración actual
$config = env_config();
var_dump([
    'environment' => $config->getEnvironment(),
    'base_url' => $config->getBaseUrl(),
    'debug' => $config->get('debug')
]);
```

## Próximos Pasos

1. **Migrar archivos existentes** gradualmente
2. **Probar en ambos entornos** después de cada cambio
3. **Documentar URLs específicas** del proyecto
4. **Capacitar al equipo** en las nuevas funciones
5. **Monitorear logs** para detectar problemas

## Soporte y Mantenimiento

- **Archivo de ejemplo**: `environment_example.php` para pruebas
- **Logs automáticos**: En desarrollo para debugging
- **Funciones helper**: Documentadas y fáciles de usar
- **Compatibilidad**: Mantenida con versiones anteriores
