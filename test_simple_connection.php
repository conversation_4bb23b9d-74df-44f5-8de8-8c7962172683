<?php
// Prueba simple de conexión
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Probando conexión directa...\n";

$host = '*************';
$port = 3306;
$dbname = 'gestarse_experian';
$username = 'gestarse_ncornejo7_experian';
$password = 'N1c0l7as17';

try {
    $conexion = new mysqli($host, $username, $password, $dbname, $port);
    
    if ($conexion->connect_error) {
        echo "Error de conexión: " . $conexion->connect_error . "\n";
        exit(1);
    }
    
    echo "✅ Conexión exitosa\n";
    
    // Probar consulta simple
    $result = $conexion->query("SELECT 1 as test");
    if ($result) {
        echo "✅ Consulta de prueba exitosa\n";
        $result->free();
    } else {
        echo "❌ Error en consulta: " . $conexion->error . "\n";
    }
    
    // Probar prepared statement
    $stmt = $conexion->prepare("SELECT COUNT(*) FROM tb_inteletgroup_prospectos WHERE rut_cliente = ?");
    if ($stmt) {
        echo "✅ Prepared statement creado\n";
        
        $test_rut = '12345678-9';
        $stmt->bind_param("s", $test_rut);
        $stmt->execute();
        $stmt->store_result();
        
        echo "✅ Prepared statement ejecutado, filas encontradas: " . $stmt->num_rows . "\n";
        $stmt->close();
    } else {
        echo "❌ Error creando prepared statement: " . $conexion->error . "\n";
    }
    
    $conexion->close();
    echo "✅ Conexión cerrada\n";
    
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
}

echo "Prueba completada.\n";
?>
