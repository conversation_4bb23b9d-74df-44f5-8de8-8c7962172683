<?php
/**
 * Funciones Helper para URLs y Rutas
 * 
 * Este archivo proporciona funciones de conveniencia para trabajar
 * con URLs y rutas de forma consistente en todos los entornos.
 * 
 * <AUTHOR> Intranet Gestar
 * @version 1.0
 */

// Prevenir ejecución directa
if (!defined('ABSPATH')) {
    die('Acceso directo no permitido');
}

/**
 * Obtiene la URL completa para un archivo o ruta
 * 
 * @param string $path Ruta relativa al archivo
 * @return string URL completa
 */
function get_url($path = '') {
    return base_url($path);
}

/**
 * Obtiene la URL para un archivo CSS con versionado automático
 * 
 * @param string $cssFile Nombre del archivo CSS
 * @return string URL completa con versión
 */
function css_url($cssFile) {
    $config = EnvironmentConfig::getInstance();
    $baseUrl = $config->url("css/{$cssFile}");
    
    // En desarrollo, agregar timestamp para evitar cache
    if ($config->isDevelopment()) {
        $separator = strpos($baseUrl, '?') !== false ? '&' : '?';
        $baseUrl .= $separator . 'v=' . time();
    }
    
    return $baseUrl;
}

/**
 * Obtiene la URL para un archivo JavaScript con versionado automático
 * 
 * @param string $jsFile Nombre del archivo JavaScript
 * @return string URL completa con versión
 */
function js_url($jsFile) {
    $config = EnvironmentConfig::getInstance();
    $baseUrl = $config->url("js/{$jsFile}");
    
    // En desarrollo, agregar timestamp para evitar cache
    if ($config->isDevelopment()) {
        $separator = strpos($baseUrl, '?') !== false ? '&' : '?';
        $baseUrl .= $separator . 'v=' . time();
    }
    
    return $baseUrl;
}

/**
 * Obtiene la URL para una imagen
 * 
 * @param string $imgFile Nombre del archivo de imagen
 * @return string URL completa
 */
function img_url($imgFile) {
    return base_url("img/{$imgFile}");
}

/**
 * Obtiene la URL para un endpoint de API
 * 
 * @param string $endpoint Nombre del endpoint
 * @return string URL completa
 */
function api_url($endpoint) {
    return base_url("endpoints/{$endpoint}");
}

/**
 * Redirige a una URL de forma segura
 * 
 * @param string $path Ruta a la que redirigir
 * @param int $statusCode Código de estado HTTP (default: 302)
 */
function redirect_to($path, $statusCode = 302) {
    $url = base_url($path);
    
    // Limpiar cualquier salida previa
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    // Enviar headers de redirección
    http_response_code($statusCode);
    header("Location: {$url}");
    exit;
}

/**
 * Redirige al login con un mensaje de error opcional
 * 
 * @param string $error Mensaje de error opcional
 */
function redirect_to_login($error = null) {
    $loginUrl = 'login.php';
    if ($error) {
        $loginUrl .= '?error=' . urlencode($error);
    }
    redirect_to($loginUrl);
}

/**
 * Obtiene la URL actual completa
 * 
 * @return string URL actual
 */
function current_url() {
    $config = EnvironmentConfig::getInstance();
    $protocol = $config->get('ssl_required') ? 'https://' : 'http://';
    
    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') {
        $protocol = 'https://';
    }
    
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $uri = $_SERVER['REQUEST_URI'] ?? '';
    
    return $protocol . $host . $uri;
}

/**
 * Verifica si la URL actual coincide con una ruta específica
 * 
 * @param string $path Ruta a verificar
 * @return bool True si coincide
 */
function is_current_page($path) {
    $currentPath = $_SERVER['REQUEST_URI'] ?? '';
    $currentPath = parse_url($currentPath, PHP_URL_PATH);
    $currentPath = basename($currentPath);
    
    return $currentPath === $path;
}

/**
 * Genera un token CSRF para formularios
 * 
 * @return string Token CSRF
 */
function csrf_token() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    
    return $_SESSION['csrf_token'];
}

/**
 * Verifica un token CSRF
 * 
 * @param string $token Token a verificar
 * @return bool True si es válido
 */
function verify_csrf_token($token) {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Obtiene la URL para descargar un documento
 * 
 * @param string $filename Nombre del archivo
 * @param string $type Tipo de documento (opcional)
 * @return string URL de descarga
 */
function download_url($filename, $type = null) {
    $params = array('file' => urlencode($filename));
    if ($type) {
        $params['type'] = urlencode($type);
    }
    
    $queryString = http_build_query($params);
    return base_url("descargar_documento.php?{$queryString}");
}

/**
 * Construye una URL con parámetros de consulta
 * 
 * @param string $path Ruta base
 * @param array $params Parámetros de consulta
 * @return string URL completa con parámetros
 */
function build_url($path, $params = array()) {
    $url = base_url($path);
    
    if (!empty($params)) {
        $separator = strpos($url, '?') !== false ? '&' : '?';
        $url .= $separator . http_build_query($params);
    }
    
    return $url;
}

/**
 * Obtiene la URL base sin protocolo (para uso en JavaScript)
 * 
 * @return string URL base sin protocolo
 */
function protocol_relative_base_url() {
    $baseUrl = base_url();
    return preg_replace('/^https?:/', '', $baseUrl);
}

/**
 * Verifica si una URL es externa
 * 
 * @param string $url URL a verificar
 * @return bool True si es externa
 */
function is_external_url($url) {
    $baseHost = parse_url(base_url(), PHP_URL_HOST);
    $urlHost = parse_url($url, PHP_URL_HOST);
    
    return $urlHost && $urlHost !== $baseHost;
}

/**
 * Sanitiza una URL para uso seguro
 * 
 * @param string $url URL a sanitizar
 * @return string URL sanitizada
 */
function sanitize_url($url) {
    // Remover caracteres peligrosos
    $url = filter_var($url, FILTER_SANITIZE_URL);
    
    // Validar que sea una URL válida
    if (filter_var($url, FILTER_VALIDATE_URL) === false) {
        return base_url();
    }
    
    return $url;
}
