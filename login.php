<?php
// Incluir el archivo de conexión
require_once 'con_db.php';
require_once 'init.php';
?>

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="GESTAR INTRANET">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">

  <!-- Cache control headers -->
  <meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, max-age=0">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>PORTAL GESTAR - LOGIN</title>

  <!-- Favicon -->
  <link rel="icon" href="img/icons/logoGestar.ico">
  <link rel="apple-touch-icon" href="img/icons/logoGestar.ico">

  <!-- Style CSS -->
  <link rel="stylesheet" href="<?php echo version_url('style.css'); ?>">

  <!-- Enhanced Login Styles -->
  <link rel="stylesheet" href="<?php echo version_url('css/enhanced-login.css'); ?>">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">

  <!-- jQuery -->
  <script src="<?php echo version_url('js/jquery-3.7.1.min.js'); ?>"></script>

  <!-- Font Awesome for enhanced icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Animate.css for smooth animations -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Enhanced Login Wrapper Area -->
  <div class="enhanced-login-wrapper d-flex align-items-center justify-content-center">
    <div class="login-container animate__animated animate__fadeInUp">

      <!-- Logo Section -->
      <div class="logo-section text-center mb-4">
        <div class="logo-container">
          <img class="login-logo animate__animated animate__pulse animate__infinite" src="logoGestar.png" alt="Gestar Logo">
        </div>
        <h2 class="brand-title">Portal Gestar</h2>
        <p class="brand-subtitle">Sistema de Gestión Empresarial</p>
      </div>

      <!-- Login Form Card -->
      <div class="login-card">
        <div class="card-header">
          <h4 class="login-title">
            <i class="fas fa-sign-in-alt me-2"></i>
            Iniciar Sesión
          </h4>
          <p class="login-subtitle">Ingresa con tus credenciales para continuar</p>
        </div>

        <!-- Enhanced Message System -->
        <div class="message-container">
          <!-- Success Message -->
          <div id="success-message" class="enhanced-message success-message" style="display: none;">
            <div class="message-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="message-content">
              <div class="message-title">¡Éxito!</div>
              <div class="message-text"></div>
            </div>
            <div class="message-progress"></div>
          </div>

          <!-- Error Message -->
          <div id="error-message" class="enhanced-message error-message" style="display: none;">
            <div class="message-icon">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="message-content">
              <div class="message-title">Error</div>
              <div class="message-text"></div>
            </div>
            <button class="message-close" onclick="hideMessage('error')">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <!-- Info Message -->
          <div id="info-message" class="enhanced-message info-message" style="display: none;">
            <div class="message-icon">
              <i class="fas fa-info-circle"></i>
            </div>
            <div class="message-content">
              <div class="message-title">Información</div>
              <div class="message-text"></div>
            </div>
            <div class="message-progress"></div>
          </div>

          <!-- Loading Message -->
          <div id="loading-message" class="enhanced-message loading-message" style="display: none;">
            <div class="message-icon">
              <div class="loading-spinner">
                <i class="fas fa-circle-notch fa-spin"></i>
              </div>
            </div>
            <div class="message-content">
              <div class="message-title">Procesando</div>
              <div class="message-text">Verificando credenciales...</div>
            </div>
            <div class="message-progress loading-progress"></div>
          </div>
        </div>

        <!-- Enhanced Form -->
        <form id="login-form" method="POST" class="enhanced-form">
          <!-- Email/RUT Input -->
          <div class="form-group enhanced-form-group">
            <label for="rut" class="form-label">
              <i class="fas fa-user me-2"></i>
              RUT
            </label>
            <div class="input-container">
              <input
                class="form-control enhanced-input"
                type="text"
                id="rut"
                name="rut"
                placeholder="Ingresa tu correo o RUT"
                required
                autocomplete="username"
              >
              <div class="input-icon">
                <i class="fas fa-user"></i>
              </div>
              <div class="input-validation">
                <i class="fas fa-check-circle validation-success"></i>
                <i class="fas fa-exclamation-circle validation-error"></i>
              </div>
            </div>
            <div class="field-feedback"></div>
          </div>

          <!-- Password Input -->
          <div class="form-group enhanced-form-group">
            <label for="clave" class="form-label">
              <i class="fas fa-lock me-2"></i>
              Contraseña
            </label>
            <div class="input-container">
              <input
                class="form-control enhanced-input"
                id="clave"
                name="clave"
                type="password"
                placeholder="Ingresa tu contraseña"
                required
                autocomplete="current-password"
              >
              <div class="input-icon">
                <i class="fas fa-lock"></i>
              </div>
              <div class="password-toggle" id="password-visibility">
                <i class="fas fa-eye"></i>
              </div>
              <div class="input-validation">
                <i class="fas fa-check-circle validation-success"></i>
                <i class="fas fa-exclamation-circle validation-error"></i>
              </div>
            </div>
            <div class="field-feedback"></div>
          </div>

          <!-- Submit Button -->
          <button class="btn enhanced-submit-btn w-100" type="submit" id="submit-btn">
            <span class="btn-content">
              <i class="fas fa-sign-in-alt me-2"></i>
              <span class="btn-text">Ingresar</span>
            </span>
            <div class="btn-loading" style="display: none;">
              <i class="fas fa-circle-notch fa-spin me-2"></i>
              <span>Procesando...</span>
            </div>
          </button>
        </form>

        <!-- Additional Info -->
        <div class="login-footer">
          <div class="security-info">
            <i class="fas fa-shield-alt me-2"></i>
            <span>Conexión segura SSL</span>
          </div>
          <div class="create-password-link text-center mt-3">
            <a href="create_password_fixed.php" id="create-password-link" class="text-decoration-none">
              <i class="fas fa-key me-2"></i>
              <span>Crear Contraseña</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>

  <script>
    // Enhanced Message System Functions
    function showMessage(type, title, message, duration = 5000) {
      // Hide all messages first
      $('.enhanced-message').removeClass('show').addClass('hide');

      setTimeout(() => {
        $('.enhanced-message').hide();

        // Show the specific message
        const messageElement = $(`#${type}-message`);
        messageElement.find('.message-title').text(title);
        messageElement.find('.message-text').text(message);

        messageElement.show().removeClass('hide').addClass('show animate__animated animate__slideInDown');

        // Auto-hide for success and info messages
        if (type === 'success' || type === 'info') {
          const progressBar = messageElement.find('.message-progress');
          progressBar.css('animation', `progress-bar ${duration}ms linear`);

          setTimeout(() => {
            hideMessage(type);
          }, duration);
        }
      }, 100);
    }

    function hideMessage(type) {
      const messageElement = $(`#${type}-message`);
      messageElement.removeClass('show').addClass('hide animate__animated animate__slideOutUp');

      setTimeout(() => {
        messageElement.hide().removeClass('animate__animated animate__slideOutUp');
      }, 500);
    }

    function showLoading(message = 'Verificando credenciales...') {
      $('.enhanced-message').removeClass('show').addClass('hide');

      setTimeout(() => {
        $('.enhanced-message').hide();

        const loadingElement = $('#loading-message');
        loadingElement.find('.message-text').text(message);
        loadingElement.show().removeClass('hide').addClass('show animate__animated animate__slideInDown');
      }, 100);
    }

    function hideLoading() {
      hideMessage('loading');
    }

    // Legacy functions for compatibility
    function mostrarError(mensaje) {
      showMessage('error', 'Error', mensaje);
    }

    function mostrarInfo(mensaje) {
      showMessage('info', 'Información', mensaje);
    }

    function mostrarExito(mensaje) {
      showMessage('success', '¡Éxito!', mensaje);
    }

    // Función para registrar errores en consola
    function logError(error, info) {
      console.error('Error en login:', error);
      console.info('Info adicional:', info);
    }

    // Input validation functions
    function validateInput(input) {
      const value = input.val().trim();
      const container = input.closest('.input-container');
      const feedback = input.closest('.enhanced-form-group').find('.field-feedback');

      if (value.length === 0) {
        container.removeClass('valid invalid');
        feedback.text('');
        return false;
      }

      // Email/RUT validation
      if (input.attr('name') === 'rut') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const rutRegex = /^\d{1,2}\.\d{3}\.\d{3}-[\dkK]$/;
        const rutSimpleRegex = /^\d{7,8}-[\dkK]$/; // RUT sin puntos

        if (emailRegex.test(value)) {
          container.removeClass('invalid').addClass('valid');
          feedback.text('Correo electrónico válido').removeClass('error').addClass('success');
          return true;
        } else if (rutRegex.test(value) || rutSimpleRegex.test(value)) {
          container.removeClass('invalid').addClass('valid');
          feedback.text('RUT válido').removeClass('error').addClass('success');
          return true;
        } else if (value.length > 3) {
          container.removeClass('invalid').addClass('valid');
          feedback.text('').removeClass('error');
          return true;
        } else {
          container.removeClass('valid').addClass('invalid');
          feedback.text('Ingresa un correo válido o RUT (ej: 12.345.678-9)').addClass('error');
          return false;
        }
      }

      // Password validation
      if (input.attr('name') === 'clave') {
        if (value.length >= 3) {
          container.removeClass('invalid').addClass('valid');
          feedback.text('').removeClass('error');
          return true;
        } else {
          container.removeClass('valid').addClass('invalid');
          feedback.text('La contraseña debe tener al menos 3 caracteres').addClass('error');
          return false;
        }
      }

      return true;
    }

    // Input icon visibility function
    function updateInputIconVisibility(input) {
      const container = input.closest('.input-container');
      const value = input.val().trim();
      const isFocused = input.is(':focus');

      // Show icon if input has content or is focused
      if (value.length > 0 || isFocused) {
        container.addClass('has-content');
      } else {
        container.removeClass('has-content');
      }
    }

    $(document).ready(function() {
      // Initialize enhanced login system
      initializeEnhancedLogin();

      // Limpiar la caché local para evitar problemas
      if ('caches' in window) {
        caches.keys().then(function(names) {
          names.forEach(function(name) {
            caches.delete(name);
          });
        });
      }

      // Prevenir resubmisión del formulario
      if (window.history.replaceState) {
        window.history.replaceState(null, null, window.location.href);
      }

      // Enhanced password visibility toggle
      $('#password-visibility').on('click', function() {
        const passwordInput = $('#clave');
        const icon = $(this).find('i');
        const type = passwordInput.attr('type') === 'password' ? 'text' : 'password';

        passwordInput.attr('type', type);

        if (type === 'text') {
          icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
          icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }

        // Add animation
        $(this).addClass('animate__animated animate__pulse');
        setTimeout(() => {
          $(this).removeClass('animate__animated animate__pulse');
        }, 600);
      });

      // Real-time input validation
      $('#rut, #clave').on('input blur', function() {
        validateInput($(this));
        updateInputIconVisibility($(this));
      });

      // Enhanced form focus effects with icon visibility
      $('.enhanced-input').on('focus', function() {
        $(this).closest('.input-container').addClass('focused');
        updateInputIconVisibility($(this));
      }).on('blur', function() {
        $(this).closest('.input-container').removeClass('focused');
        updateInputIconVisibility($(this));
      });

      // Initialize input icon visibility on page load
      $('.enhanced-input').each(function() {
        updateInputIconVisibility($(this));
      });

      // Enhanced form submission handler
      $('#login-form').on('submit', function(e) {
        e.preventDefault();

        // Validate all inputs
        const rutValid = validateInput($('#rut'));
        const claveValid = validateInput($('#clave'));

        if (!rutValid || !claveValid) {
          showMessage('error', 'Campos Incompletos', 'Por favor complete todos los campos correctamente');
          return;
        }

        // Show loading state
        showLoading('Verificando credenciales...');

        // Enhanced button state
        const submitBtn = $('#submit-btn');
        submitBtn.prop('disabled', true).addClass('loading');
        submitBtn.find('.btn-content').hide();
        submitBtn.find('.btn-loading').show();

        // Obtener valores del formulario
        const rut = $('#rut').val().trim();
        const clave = $('#clave').val().trim();

        // Add form shake animation for better UX
        $('.login-card').addClass('animate__animated animate__pulse');

        // Crear FormData para enviar datos
        const formData = new FormData();
        formData.append('rut', rut);
        formData.append('clave', clave);

        // Configurar timeout para la petición
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 segundos timeout

        // Realizar la solicitud fetch en lugar de ajax
        fetch('ControllerGestar.php', {
          method: 'POST',
          body: formData,
          signal: controller.signal,
          cache: 'no-store'
        })
        .then(response => {
          clearTimeout(timeoutId);
          
          // Verificar si la respuesta es OK
          if (!response.ok) {
            throw new Error(`Error HTTP: ${response.status} ${response.statusText}`);
          }
          
          // Verificar el tipo de contenido
          const contentType = response.headers.get('content-type');
          if (!contentType || !contentType.includes('application/json')) {
            throw new Error(`Tipo de contenido inesperado: ${contentType}`);
          }
          
          return response.json();
        })
        .then(data => {
          console.log('Respuesta del servidor:', data);
          hideLoading();

          if (data.success) {
            // Login exitoso - mostrar mensaje de éxito
            const projectName = data.proyecto === 'inteletGroup' ? 'InteletGroup' : 'Experian';
            showMessage('success', '¡Bienvenido!', `Acceso autorizado. Redirigiendo al panel de ${projectName}...`, 2000);
            
            // Enhanced success animation
            $('.login-card').removeClass('animate__pulse').addClass('animate__bounceIn');
            const submitBtn = $('#submit-btn');
            submitBtn.removeClass('loading').addClass('success');

            // Retrasar la redirección para mostrar el mensaje
            setTimeout(function() {
              console.log('Datos de redirección:', data);
              // Verificar si hay una URL de redirección en la respuesta
              if (data.redirect) {
                console.log('Redirigiendo a:', data.redirect);
                window.location.href = data.redirect;
              } else {
                // URL por defecto si no hay redirección específica
                console.log('Redirigiendo a URL por defecto');
                window.location.href = 'form_experian.php';
              }
            }, 2000);
          }
        })
        .catch(error => {
          clearTimeout(timeoutId);
          hideLoading();

          // Enhanced error handling with specific messages
          let errorTitle = 'Error de Conexión';
          let errorMessage = '';

          if (error.name === 'AbortError') {
            errorTitle = 'Tiempo Agotado';
            errorMessage = 'La solicitud ha excedido el tiempo de espera. Por favor inténtelo nuevamente.';
          } else if (error.message.includes('Failed to fetch')) {
            errorTitle = 'Sin Conexión';
            errorMessage = 'No se pudo conectar con el servidor. Verifique su conexión a internet.';
          } else {
            errorMessage = 'Error en la conexión: ' + error.message;
          }

          showMessage('error', errorTitle, errorMessage);

          // Enhanced error animation
          $('.login-card').removeClass('animate__pulse').addClass('animate__shakeX');
          setTimeout(() => {
            $('.login-card').removeClass('animate__animated animate__shakeX');
          }, 1000);

          logError(error, { rut: rut });
          resetSubmitButton();
        });
      });

      // Check for URL parameters and show appropriate messages
      const urlParams = new URLSearchParams(window.location.search);
      const errorParam = urlParams.get('error');

      if (errorParam) {
        setTimeout(() => {
          if (errorParam === '1') {
            showMessage('error', 'Acceso Denegado', 'Credenciales incorrectas');
          } else if (errorParam === 'timeout') {
            showMessage('error', 'Sesión Expirada', 'Su sesión ha expirado. Por favor inicie sesión nuevamente.');
          } else if (errorParam === 'unauthorized') {
            showMessage('error', 'No Autorizado', 'No tiene permisos para acceder a esta sección.');
          } else {
            showMessage('error', 'Error', decodeURIComponent(errorParam));
          }
        }, 500);
      }
    });

    // Helper functions
    function resetSubmitButton() {
      const submitBtn = $('#submit-btn');
      submitBtn.prop('disabled', false).removeClass('loading success');
      submitBtn.find('.btn-content').show();
      submitBtn.find('.btn-loading').hide();
    }

    function initializeEnhancedLogin() {
      // Hide all messages initially
      $('.enhanced-message').hide();

      // Add entrance animation to login container
      setTimeout(() => {
        $('.login-container').addClass('animate__animated animate__fadeInUp');
      }, 100);

      // Initialize tooltips if needed
      if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
          return new bootstrap.Tooltip(tooltipTriggerEl);
        });
      }
    }
  </script>
</body>

</html>