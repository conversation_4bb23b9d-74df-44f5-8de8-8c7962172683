<?php
// Script para probar el flujo completo con documentos en producción
echo "=== PRUEBA COMPLETA CON DOCUMENTOS EN PRODUCCIÓN ===\n";

// Configuración
$baseUrl = 'https://www.gestarservicios.cl/intranet/dist/';
$username = '11111111-1';
$password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';

// Inicializar cURL con cookies
$cookieFile = tempnam(sys_get_temp_dir(), 'cookies');
echo "Archivo de cookies: $cookieFile\n\n";

// Función para hacer solicitudes cURL
function makeCurlRequest($url, $postData = null, $files = null, $cookieFile = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    if ($cookieFile) {
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
    }
    
    if ($postData !== null) {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($files) {
            // Para archivos, usar multipart/form-data
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        } else {
            // Para datos normales
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/x-www-form-urlencoded',
                'Accept: application/json',
                'X-Requested-With: XMLHttpRequest'
            ]);
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'response' => $response,
        'httpCode' => $httpCode,
        'error' => $error
    ];
}

// Paso 1: Login
echo "1. Realizando login...\n";
$loginData = [
    'rut' => $username,
    'clave' => $password
];

$loginResult = makeCurlRequest($baseUrl . 'ControllerGestar.php', $loginData, null, $cookieFile);
echo "   Código HTTP: {$loginResult['httpCode']}\n";

if ($loginResult['httpCode'] === 200) {
    $loginJson = json_decode($loginResult['response'], true);
    if ($loginJson && isset($loginJson['success']) && $loginJson['success']) {
        echo "   ✅ Login exitoso\n";
    } else {
        echo "   ❌ Login fallido\n";
        exit(1);
    }
} else {
    echo "   ❌ Error en login\n";
    exit(1);
}

// Paso 2: Crear prospecto
echo "\n2. Creando prospecto...\n";
$uniqueRut = rand(10000000, 99999999) . '-' . rand(0, 9);

$prospectData = [
    'usuario_id' => '4',
    'rut_cliente' => $uniqueRut,
    'razon_social' => 'EMPRESA PRUEBA CON DOCUMENTOS LTDA',
    'nombre_ejecutivo' => 'Ejecutivo Documentos',
    'rubro' => 'Servicios con documentos',
    'direccion_comercial' => 'Dirección Documentos 123',
    'telefono_celular' => '*********',
    'email' => '<EMAIL>',
    'tipo_persona' => 'Jurídica',
    'numero_pos' => '54321',
    'tipo_cuenta' => 'Corriente',
    'numero_cuenta_bancaria' => '*********',
    'dias_atencion' => 'Lunes a Viernes',
    'horario_atencion' => '8:00 - 17:00',
    'contrata_boleta' => 'Factura',
    'competencia_actual' => 'Transbank'
];

echo "   RUT generado: $uniqueRut\n";

$prospectResult = makeCurlRequest($baseUrl . 'guardar_prospecto_inteletgroup.php', $prospectData, null, $cookieFile);
echo "   Código HTTP: {$prospectResult['httpCode']}\n";

$prospectJson = json_decode($prospectResult['response'], true);
if ($prospectJson && $prospectJson['success']) {
    $prospecto_id = $prospectJson['prospecto_id'];
    echo "   ✅ Prospecto creado exitosamente - ID: $prospecto_id\n";
} else {
    echo "   ❌ Error creando prospecto: " . ($prospectJson['message'] ?? 'Error desconocido') . "\n";
    exit(1);
}

// Paso 3: Crear documentos de prueba
echo "\n3. Creando documentos de prueba...\n";
$testFiles = [];

// Crear PDF de prueba
$pdfContent = "%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n178\n%%EOF";
$pdfPath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'documento_prueba.pdf';
file_put_contents($pdfPath, $pdfContent);
$testFiles[] = ['path' => $pdfPath, 'name' => 'documento_prueba.pdf', 'type' => 'application/pdf'];

// Crear imagen de prueba (PNG simple)
$imgContent = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAHGbKdMDgAAAABJRU5ErkJggg==');
$imgPath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'imagen_prueba.png';
file_put_contents($imgPath, $imgContent);
$testFiles[] = ['path' => $imgPath, 'name' => 'imagen_prueba.png', 'type' => 'image/png'];

echo "   ✅ Documentos de prueba creados\n";

// Paso 4: Subir documentos
echo "\n4. Subiendo documentos...\n";
foreach ($testFiles as $index => $file) {
    echo "   Subiendo: {$file['name']}\n";
    
    // Preparar datos para multipart/form-data
    $postData = [
        'prospecto_id' => $prospecto_id,
        'rut_cliente' => $uniqueRut,
        'tipo_documento_id' => '1', // ID genérico
        'documento' => new CURLFile($file['path'], $file['type'], $file['name'])
    ];
    
    $uploadResult = makeCurlRequest($baseUrl . 'inteletgroup_upload_document.php', $postData, true, $cookieFile);
    echo "     Código HTTP: {$uploadResult['httpCode']}\n";
    
    if ($uploadResult['httpCode'] === 200 || $uploadResult['httpCode'] === 302) {
        echo "     ✅ Documento subido exitosamente\n";
    } else {
        echo "     ❌ Error subiendo documento\n";
        echo "     Respuesta: " . substr($uploadResult['response'], 0, 200) . "...\n";
    }
}

// Paso 5: Verificar documentos subidos
echo "\n5. Verificando documentos subidos...\n";
$docsResult = makeCurlRequest($baseUrl . "inteletgroup_documentos_enhanced.php?prospecto_id=$prospecto_id", null, null, $cookieFile);
echo "   Código HTTP: {$docsResult['httpCode']}\n";

if ($docsResult['httpCode'] === 200) {
    echo "   ✅ Página de documentos accesible\n";
    
    // Buscar indicadores de documentos en la respuesta
    if (strpos($docsResult['response'], 'documento_prueba.pdf') !== false) {
        echo "   ✅ PDF encontrado en la página\n";
    }
    if (strpos($docsResult['response'], 'imagen_prueba.png') !== false) {
        echo "   ✅ Imagen encontrada en la página\n";
    }
} else {
    echo "   ❌ Error accediendo a página de documentos\n";
}

// Limpiar archivos temporales
echo "\n6. Limpiando archivos temporales...\n";
foreach ($testFiles as $file) {
    if (file_exists($file['path'])) {
        unlink($file['path']);
        echo "   ✅ Eliminado: {$file['name']}\n";
    }
}

if (file_exists($cookieFile)) {
    unlink($cookieFile);
    echo "   ✅ Eliminado archivo de cookies\n";
}

echo "\n=== RESUMEN ===\n";
echo "✅ Login exitoso\n";
echo "✅ Prospecto creado - ID: $prospecto_id\n";
echo "✅ RUT: $uniqueRut\n";
echo "✅ Documentos procesados\n";
echo "\n=== FIN PRUEBA COMPLETA CON DOCUMENTOS ===\n";
?>
