<?php
// Script para probar la corrección del error de documentos
echo "=== PRUEBA CORRECCIÓN ERROR DOCUMENTOS ===\n";

// Configuración
$baseUrl = 'https://www.gestarservicios.cl/intranet/dist/';
$username = '11111111-1';
$password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';

function makeCurlRequestWithFiles($url, $postData, $files, $cookieFile = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    if ($cookieFile) {
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
    }
    
    curl_setopt($ch, CURLOPT_POST, true);
    
    // Combinar datos POST y archivos
    $allData = array_merge($postData, $files);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $allData);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'response' => $response,
        'httpCode' => $httpCode,
        'error' => $error
    ];
}

function makeCurlRequest($url, $postData = null, $cookieFile = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    if ($cookieFile) {
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
    }
    
    if ($postData !== null) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json',
            'X-Requested-With: XMLHttpRequest'
        ]);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'response' => $response,
        'httpCode' => $httpCode,
        'error' => $error
    ];
}

// Paso 1: Login
echo "1. Realizando login...\n";
$cookieFile = tempnam(sys_get_temp_dir(), 'cookies');

$loginData = [
    'rut' => $username,
    'clave' => $password
];

$loginResult = makeCurlRequest($baseUrl . 'ControllerGestar.php', $loginData, $cookieFile);
if ($loginResult['httpCode'] === 200) {
    $loginJson = json_decode($loginResult['response'], true);
    if ($loginJson && isset($loginJson['success']) && $loginJson['success']) {
        echo "   ✅ Login exitoso\n";
    } else {
        echo "   ❌ Login fallido\n";
        exit(1);
    }
} else {
    echo "   ❌ Error en login\n";
    exit(1);
}

// Paso 2: Crear archivo de prueba
echo "\n2. Creando archivo de prueba...\n";
$testContent = 'Contenido de prueba para documento - ' . date('Y-m-d H:i:s');
$testFilePath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'test_document.txt';
file_put_contents($testFilePath, $testContent);

$testFile = new CURLFile($testFilePath, 'text/plain', 'test_document.txt');
echo "   ✅ Archivo creado: test_document.txt\n";

// Paso 3: Crear prospecto con documento
echo "\n3. Creando prospecto con documento...\n";
$uniqueRut = rand(10000000, 99999999) . '-' . rand(0, 9);

$prospectData = [
    'usuario_id' => '12',
    'nombre_ejecutivo' => 'Usuario Experian Test',
    'rut_cliente' => $uniqueRut,
    'razon_social' => 'EMPRESA PRUEBA CORRECCIÓN LTDA',
    'rubro' => 'Servicios de prueba corrección',
    'direccion_comercial' => 'Dirección Corrección 123',
    'telefono_celular' => '*********',
    'email' => '<EMAIL>',
    'tipo_persona' => 'Natural',
    'numero_pos' => 'POS123456',
    'tipo_cuenta' => 'Cuenta Vista',
    'numero_cuenta_bancaria' => '********',
    'dias_atencion' => 'LUNES A VIERNES',
    'horario_atencion' => '09:00 - 18:00',
    'contrata_boleta' => 'Si',
    'competencia_actual' => 'Transbank'
];

$files = [
    'doc_PN_CEDULA_FRONTAL_0' => $testFile
];

echo "   RUT: $uniqueRut\n";

$prospectResult = makeCurlRequestWithFiles($baseUrl . 'guardar_prospecto_inteletgroup.php', $prospectData, $files, $cookieFile);
echo "   Código HTTP: {$prospectResult['httpCode']}\n";

if ($prospectResult['httpCode'] === 200) {
    echo "   Respuesta: {$prospectResult['response']}\n";
    
    $prospectJson = json_decode($prospectResult['response'], true);
    if ($prospectJson) {
        if ($prospectJson['success']) {
            echo "   ✅ PROSPECTO CON DOCUMENTO CREADO EXITOSAMENTE - ID: {$prospectJson['prospecto_id']}\n";
            
            if (isset($prospectJson['documentos']) && !empty($prospectJson['documentos'])) {
                echo "   📄 Documentos procesados:\n";
                foreach ($prospectJson['documentos'] as $doc) {
                    echo "     - Campo: {$doc['campo']}\n";
                    echo "       Archivo: {$doc['archivo']}\n";
                    echo "       Ruta: {$doc['ruta']}\n";
                }
            }
            
            if (isset($prospectJson['upload_errors']) && !empty($prospectJson['upload_errors'])) {
                echo "   ⚠️ Errores de carga:\n";
                foreach ($prospectJson['upload_errors'] as $error) {
                    echo "     - $error\n";
                }
            }
        } else {
            echo "   ❌ ERROR AL CREAR PROSPECTO: {$prospectJson['message']}\n";
        }
    } else {
        echo "   ❌ RESPUESTA NO ES JSON VÁLIDO\n";
        echo "   Primeros 200 caracteres: " . substr($prospectResult['response'], 0, 200) . "\n";
    }
} else {
    echo "   ❌ ERROR HTTP: {$prospectResult['httpCode']}\n";
    echo "   Error cURL: {$prospectResult['error']}\n";
}

// Paso 4: Intentar duplicado para verificar que no se cree
echo "\n4. Probando duplicado...\n";
$duplicateResult = makeCurlRequestWithFiles($baseUrl . 'guardar_prospecto_inteletgroup.php', $prospectData, $files, $cookieFile);
echo "   Código HTTP: {$duplicateResult['httpCode']}\n";

$duplicateJson = json_decode($duplicateResult['response'], true);
if ($duplicateJson) {
    if (!$duplicateJson['success'] && isset($duplicateJson['duplicate']) && $duplicateJson['duplicate']) {
        echo "   ✅ DUPLICADO DETECTADO CORRECTAMENTE: {$duplicateJson['message']}\n";
    } else {
        echo "   🚨 PROBLEMA: Duplicado no detectado correctamente\n";
    }
} else {
    echo "   ❌ Respuesta duplicado no es JSON válido\n";
}

// Limpiar
if (file_exists($testFilePath)) {
    unlink($testFilePath);
    echo "\n5. Archivo temporal eliminado\n";
}

if (file_exists($cookieFile)) {
    unlink($cookieFile);
}

echo "\n=== PRUEBA COMPLETADA ===\n";
?>
