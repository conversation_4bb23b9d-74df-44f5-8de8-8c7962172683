<?php
// Prueba simple de prevención de duplicados
echo "=== PRUEBA SIMPLE PREVENCIÓN DUPLICADOS ===\n";

// Configuración
$baseUrl = 'https://www.gestarservicios.cl/intranet/dist/';
$username = '11111111-1';
$password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';

function makeCurlRequest($url, $postData = null, $cookieFile = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    if ($cookieFile) {
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
    }
    
    if ($postData !== null) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json',
            'X-Requested-With: XMLHttpRequest'
        ]);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'response' => $response,
        'httpCode' => $httpCode,
        'error' => $error
    ];
}

// Login
echo "1. Login...\n";
$cookieFile = tempnam(sys_get_temp_dir(), 'cookies');

$loginData = ['rut' => $username, 'clave' => $password];
$loginResult = makeCurlRequest($baseUrl . 'ControllerGestar.php', $loginData, $cookieFile);

if ($loginResult['httpCode'] === 200) {
    $loginJson = json_decode($loginResult['response'], true);
    if ($loginJson && $loginJson['success']) {
        echo "   ✅ Login exitoso\n";
    } else {
        echo "   ❌ Login fallido\n";
        exit(1);
    }
} else {
    echo "   ❌ Error en login\n";
    exit(1);
}

// Crear prospecto
echo "\n2. Creando prospecto...\n";
$uniqueRut = rand(10000000, 99999999) . '-' . rand(0, 9);

$prospectData = [
    'usuario_id' => '4',
    'rut_cliente' => $uniqueRut,
    'razon_social' => 'EMPRESA PRUEBA SIMPLE LTDA',
    'nombre_ejecutivo' => 'Ejecutivo Simple',
    'rubro' => 'Servicios de prueba simple',
    'direccion_comercial' => 'Dirección Simple 123',
    'telefono_celular' => '*********',
    'email' => '<EMAIL>',
    'tipo_persona' => 'Jurídica',
    'numero_pos' => '54321',
    'tipo_cuenta' => 'Corriente',
    'numero_cuenta_bancaria' => '*********',
    'dias_atencion' => 'Lunes a Viernes',
    'horario_atencion' => '8:00 - 17:00',
    'contrata_boleta' => 'Factura',
    'competencia_actual' => 'Transbank'
];

echo "   RUT: $uniqueRut\n";

// Primera solicitud
echo "\n3. Primera solicitud...\n";
$result1 = makeCurlRequest($baseUrl . 'guardar_prospecto_inteletgroup.php', $prospectData, $cookieFile);
echo "   HTTP: {$result1['httpCode']}\n";

$json1 = json_decode($result1['response'], true);
if ($json1) {
    if ($json1['success']) {
        echo "   ✅ PRIMERA SOLICITUD EXITOSA - ID: {$json1['prospecto_id']}\n";
    } else {
        echo "   ❌ PRIMERA SOLICITUD FALLÓ: {$json1['message']}\n";
    }
} else {
    echo "   ❌ RESPUESTA INVÁLIDA\n";
}

// Segunda solicitud (debería fallar por duplicado)
echo "\n4. Segunda solicitud (mismo RUT)...\n";
$result2 = makeCurlRequest($baseUrl . 'guardar_prospecto_inteletgroup.php', $prospectData, $cookieFile);
echo "   HTTP: {$result2['httpCode']}\n";

$json2 = json_decode($result2['response'], true);
if ($json2) {
    if ($json2['success']) {
        echo "   🚨 PROBLEMA: SEGUNDA SOLICITUD TAMBIÉN FUE EXITOSA - ID: {$json2['prospecto_id']}\n";
    } else {
        echo "   ✅ SEGUNDA SOLICITUD RECHAZADA CORRECTAMENTE: {$json2['message']}\n";
    }
} else {
    echo "   ❌ RESPUESTA INVÁLIDA\n";
}

// Limpiar
if (file_exists($cookieFile)) {
    unlink($cookieFile);
}

echo "\n=== PRUEBA COMPLETADA ===\n";
?>
