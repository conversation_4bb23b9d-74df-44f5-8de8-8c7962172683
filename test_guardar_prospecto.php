<?php
// Script de prueba para verificar el guardado de prospectos
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>Test de guardado de prospecto</h2>";

// Incluir archivo de conexión
echo "<p>1. Incluyendo archivo de conexión...</p>";
require_once 'con_db.php';

// Verificar conexión
if (!isset($conexion)) {
    die("ERROR: No se pudo establecer conexión con la base de datos");
}
echo "<p>✓ Conexión establecida</p>";

// Verificar tabla de prospectos
echo "<p>2. Verificando tabla tb_inteletgroup_prospectos...</p>";
$result = $conexion->query("SHOW TABLES LIKE 'tb_inteletgroup_prospectos'");
if ($result->num_rows == 0) {
    die("ERROR: La tabla tb_inteletgroup_prospectos no existe");
}
echo "<p>✓ Tabla encontrada</p>";

// Verificar tabla de documentos
echo "<p>3. Verificando tabla tb_inteletgroup_documentos...</p>";
$result = $conexion->query("SHOW TABLES LIKE 'tb_inteletgroup_documentos'");
if ($result->num_rows == 0) {
    echo "<p>⚠️ ADVERTENCIA: La tabla tb_inteletgroup_documentos no existe</p>";
} else {
    echo "<p>✓ Tabla de documentos encontrada</p>";
}

// Verificar directorio de uploads
echo "<p>4. Verificando directorio de uploads...</p>";
$upload_dir = 'uploads/inteletgroup/' . date('Y/m/');
if (!file_exists('uploads/')) {
    echo "<p>⚠️ ADVERTENCIA: El directorio 'uploads/' no existe. Se creará automáticamente.</p>";
} else {
    echo "<p>✓ Directorio uploads existe</p>";
}

// Datos de prueba
echo "<h3>5. Probando inserción de datos de prueba...</h3>";
$test_data = [
    'usuario_id' => 1,
    'nombre_ejecutivo' => 'TEST EJECUTIVO',
    'rut_cliente' => '********-1',
    'razon_social' => 'EMPRESA TEST SIMPLE',
    'rubro' => 'COMERCIO',
    'direccion_comercial' => 'DIRECCION TEST 123',
    'telefono_celular' => '*********',
    'email' => '<EMAIL>',
    'tipo_persona' => 'Natural',
    'numero_pos' => 'POS123',
    'tipo_cuenta' => 'Cuenta Vista',
    'numero_cuenta_bancaria' => '********',
    'dias_atencion' => 'Lunes a Viernes',
    'horario_atencion' => '09:00 - 18:00',
    'contrata_boleta' => 'Si',
    'competencia_actual' => 'Transbank'
];

try {
    // Verificar si ya existe un registro reciente con el mismo RUT
    $checkSql = "SELECT id FROM tb_inteletgroup_prospectos WHERE rut_cliente = ? AND fecha_registro >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)";
    $checkStmt = $conexion->prepare($checkSql);
    $checkStmt->bind_param("s", $test_data['rut_cliente']);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();
    
    if ($checkResult->num_rows > 0) {
        echo "<p>⚠️ Ya existe un prospecto con RUT {$test_data['rut_cliente']} creado en los últimos 5 minutos</p>";
    } else {
        // Insertar datos de prueba
        $sql = "INSERT INTO tb_inteletgroup_prospectos (
            usuario_id, nombre_ejecutivo, rut_cliente, razon_social, rubro,
            direccion_comercial, telefono_celular, email, tipo_persona, numero_pos,
            tipo_cuenta, numero_cuenta_bancaria, dias_atencion, horario_atencion,
            contrata_boleta, competencia_actual
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conexion->prepare($sql);
        $stmt->bind_param("isssssssssssssss",
            $test_data['usuario_id'],
            $test_data['nombre_ejecutivo'],
            $test_data['rut_cliente'],
            $test_data['razon_social'],
            $test_data['rubro'],
            $test_data['direccion_comercial'],
            $test_data['telefono_celular'],
            $test_data['email'],
            $test_data['tipo_persona'],
            $test_data['numero_pos'],
            $test_data['tipo_cuenta'],
            $test_data['numero_cuenta_bancaria'],
            $test_data['dias_atencion'],
            $test_data['horario_atencion'],
            $test_data['contrata_boleta'],
            $test_data['competencia_actual']
        );
        
        if ($stmt->execute()) {
            $insert_id = $conexion->insert_id;
            echo "<p>✓ Datos insertados correctamente. ID: $insert_id</p>";
        } else {
            echo "<p>❌ Error al insertar: " . $stmt->error . "</p>";
        }
        $stmt->close();
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<h3>Resumen:</h3>";
echo "<p>Si todos los checks pasaron, el guardado de prospectos debería funcionar correctamente.</p>";
echo "<p>Si ves errores PHP en el guardado real, verifica:</p>";
echo "<ul>";
echo "<li>Que el archivo con_db.php no tenga espacios o salidas antes de <?php</li>";
echo "<li>Que no haya warnings o notices en ningún archivo incluido</li>";
echo "<li>Que los permisos del directorio uploads/ sean correctos (777)</li>";
echo "</ul>";

$conexion->close();
?>