# Estrategia de Manejo de Memoria Caché - TQW

## Descripción General

Esta documentación describe la estrategia implementada en `Tecnico_Home_LOGIS_TEST_V2.php` para el manejo de memoria caché, sesiones y navegación. La estrategia combina caché HTTP, sesiones PHP persistentes y localStorage para garantizar una experiencia de usuario fluida.

## 1. Configuración de Encabezados HTTP

### Implementación
```php
// Encabezados para permitir navegación manteniendo sesión
header('Content-Type: text/html; charset=UTF-8');

// Configuración de caché optimizada
header('Cache-Control: private, must-revalidate');
header('Pragma: private');
```

### Explicación
- **`Cache-Control: private`**: El contenido solo puede ser cacheado por el navegador del usuario final, no por proxies intermedios
- **`must-revalidate`**: Fuerza al navegador a validar el contenido con el servidor antes de usar la versión cacheada
- **`Pragma: private`**: Compatibilidad con proxies HTTP/1.0

### Beneficios
- Permite navegación hacia atrás sin perder funcionalidad
- Protege datos sensibles de ser cacheados en proxies
- Garantiza contenido actualizado mediante validación

## 2. Configuración de Sesiones PHP

### Implementación
```php
// Configuración de cookies de sesión
ini_set('session.use_cookies', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_lifetime', 86400); // 24 horas
ini_set('session.gc_maxlifetime', 86400);  // 24 horas

// Iniciar sesión si no está activa
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
```

### Configuración de Recuperación
```php
// Verificar si venimos de navegación hacia atrás
$referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
if (strpos($referer, $_SERVER['HTTP_HOST']) !== false) {
    // Regenerar ID de sesión sin destruir datos
    session_regenerate_id(false);
}
```

### Características Clave
- **Persistencia**: 24 horas de duración para cookies y datos de sesión
- **Seguridad**: Solo cookies, no URL parameters
- **Recuperación**: Regeneración automática de ID sin pérdida de datos
- **Validación**: Verificación de referer para sesiones del mismo dominio

## 3. Sistema de localStorage para Navegación

### Registro de Historial
```javascript
// Mantener historial de navegación
if (typeof localStorage !== "undefined") {
    var currentPage = window.location.href;
    var previousPages = JSON.parse(localStorage.getItem("navigationHistory") || "[]");
    
    // Añadir página actual si no existe
    if (previousPages.indexOf(currentPage) === -1) {
        previousPages.push(currentPage);
        // Mantener solo últimas 5 páginas
        if (previousPages.length > 5) {
            previousPages = previousPages.slice(-5);
        }
        localStorage.setItem("navigationHistory", JSON.stringify(previousPages));
    }
}
```

### Diagnóstico de Sesión
```php
// Registrar estado de sesión para diagnóstico
echo "<script>
if (typeof localStorage !== 'undefined') {
    localStorage.setItem('ultimaSesionActiva', '" . date('Y-m-d H:i:s') . "');
    localStorage.setItem('ultimoRutActivo', '" . $_SESSION['rut'] . "');
    localStorage.setItem('ultimaPaginaConSesion', window.location.href);
}
</script>";
```

### Funcionalidad del Botón "Volver"
```javascript
document.getElementById("btnVolver").addEventListener("click", function(e) {
    e.preventDefault();
    
    // Prioridad 1: Historial del navegador
    if (window.history.length > 1) {
        window.history.go(-1);
        return;
    }
    
    // Prioridad 2: localStorage
    if (typeof localStorage !== "undefined") {
        var previousPages = JSON.parse(localStorage.getItem("navigationHistory") || "[]");
        if (previousPages.length > 1) {
            var prevPage = previousPages[previousPages.length - 2];
            window.location.href = prevPage;
            return;
        }
    }
    
    // Fallback: Página segura
    window.location.href = "activity_dashboard.php";
});
```

## 4. Validación y Recuperación de Sesiones

### Verificación de Variables de Sesión
```php
// Verificar existencia de variables críticas
if (
    ((!isset($_SESSION['rut']) || empty($_SESSION['rut'])) &&
    (!isset($_SESSION['id_sesion']) || empty($_SESSION['id_sesion'])))
) {
    // Lógica de recuperación o redirección
}
```

### Consultas SQL Adaptables
```php
// Prioridad por RUT
if (!empty($rut_usuario)) {
    $sql = "SELECT ... FROM tb_user_tqw WHERE rut = '$rut_usuario'";
} elseif (!empty($sesion)) {
    // Fallback por token de sesión
    $sql = "SELECT ... FROM TB_LOG_APP WHERE TOKEN = '$sesion'";
}
```

### Actualización de Datos de Sesión
```php
// Sincronizar datos de sesión con base de datos
$_SESSION['rut'] = $row['RUT'];
$_SESSION['nombre'] = $row['nombre'];
$_SESSION['id_usuario'] = $row['id'];
$_SESSION['perfil'] = $row['PERFIL'];
```

## 5. Implementación en Otros Proyectos

### Checklist de Implementación

#### Encabezados HTTP
- [ ] Configurar `Cache-Control: private, must-revalidate`
- [ ] Añadir `Pragma: private` para compatibilidad
- [ ] Establecer `Content-Type` apropiado

#### Sesiones PHP
- [ ] Configurar duración de cookies (`session.cookie_lifetime`)
- [ ] Establecer tiempo de vida de datos (`session.gc_maxlifetime`)
- [ ] Implementar verificación de estado de sesión
- [ ] Añadir regeneración de ID sin destruir datos

#### localStorage
- [ ] Implementar historial de navegación (máximo 5 páginas)
- [ ] Registrar estado de sesión para diagnóstico
- [ ] Crear botón "Volver" con múltiples fallbacks

#### Validación y Recuperación
- [ ] Verificar variables de sesión críticas
- [ ] Implementar consultas SQL adaptables
- [ ] Sincronizar datos de sesión con base de datos
- [ ] Añadir logging para diagnóstico

### Archivos de Referencia
- **Archivo principal**: `Tecnico_Home_LOGIS_TEST_V2.php`
- **Líneas clave**:
  - Encabezados HTTP: líneas 10-13
  - Configuración de sesión: líneas 16-19
  - Recuperación de sesión: línea 135
  - localStorage: líneas 202-208
  - Historial navegación: líneas 73-83
  - Botón volver: líneas 87-110

### Consideraciones de Seguridad
- Nunca cachear datos sensibles en proxies públicos
- Validar referer para prevenir ataques de sesión
- Usar regeneración de ID sin destruir datos
- Implementar logging para auditoría

### Consideraciones de Rendimiento
- Limitar historial de navegación (máximo 5 páginas)
- Usar consultas SQL optimizadas con índices apropiados
- Implementar timeout apropiado para sesiones (24 horas)
- Validar existencia de localStorage antes de usar

## 6. Troubleshooting Común

### Problemas de Sesión
- **Síntoma**: Sesiones se pierden frecuentemente
- **Solución**: Verificar configuración de `session.gc_maxlifetime`

### Problemas de Navegación
- **Síntoma**: Botón "Volver" no funciona
- **Solución**: Verificar historial en localStorage y `window.history.length`

### Problemas de Caché
- **Síntoma**: Contenido obsoleto se muestra
- **Solución**: Verificar encabezados `Cache-Control` y `must-revalidate`

## Conclusión

Esta estrategia proporciona una experiencia de usuario robusta mediante:
- Caché inteligente que permite navegación fluida
- Sesiones persistentes con recuperación automática
- Historial de navegación con múltiples fallbacks
- Diagnóstico integrado para troubleshooting

La implementación es modular y puede adaptarse a diferentes proyectos manteniendo los principios fundamentales de seguridad y rendimiento.