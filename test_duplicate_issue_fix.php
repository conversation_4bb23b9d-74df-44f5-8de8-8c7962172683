<?php
// Script para probar la corrección del problema de duplicados
echo "=== PRUEBA CORRECCIÓN PROBLEMA DUPLICADOS ===\n";

// Configuración
$baseUrl = 'https://www.gestarservicios.cl/intranet/dist/';
$username = '11111111-1';
$password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';

// Inicializar cURL con cookies
$cookieFile = tempnam(sys_get_temp_dir(), 'cookies');

function makeCurlRequest($url, $postData = null, $cookieFile = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    if ($cookieFile) {
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
    }
    
    if ($postData !== null) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json',
            'X-Requested-With: XMLHttpRequest'
        ]);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'response' => $response,
        'httpCode' => $httpCode,
        'error' => $error
    ];
}

// Paso 1: Login
echo "1. Realizando login...\n";
$loginData = [
    'rut' => $username,
    'clave' => $password
];

$loginResult = makeCurlRequest($baseUrl . 'ControllerGestar.php', $loginData, $cookieFile);
if ($loginResult['httpCode'] === 200) {
    $loginJson = json_decode($loginResult['response'], true);
    if ($loginJson && isset($loginJson['success']) && $loginJson['success']) {
        echo "   ✅ Login exitoso\n";
    } else {
        echo "   ❌ Login fallido\n";
        exit(1);
    }
} else {
    echo "   ❌ Error en login\n";
    exit(1);
}

// Paso 2: Crear prospecto nuevo
echo "\n2. Creando prospecto nuevo...\n";
$uniqueRut = rand(10000000, 99999999) . '-' . rand(0, 9);

$prospectData = [
    'usuario_id' => '4',
    'rut_cliente' => $uniqueRut,
    'razon_social' => 'EMPRESA PRUEBA DUPLICADOS LTDA',
    'nombre_ejecutivo' => 'Ejecutivo Duplicados',
    'rubro' => 'Servicios de prueba duplicados',
    'direccion_comercial' => 'Dirección Duplicados 123',
    'telefono_celular' => '*********',
    'email' => '<EMAIL>',
    'tipo_persona' => 'Jurídica',
    'numero_pos' => '54321',
    'tipo_cuenta' => 'Corriente',
    'numero_cuenta_bancaria' => '*********',
    'dias_atencion' => 'Lunes a Viernes',
    'horario_atencion' => '8:00 - 17:00',
    'contrata_boleta' => 'Factura',
    'competencia_actual' => 'Transbank'
];

echo "   RUT generado: $uniqueRut\n";

$prospectResult = makeCurlRequest($baseUrl . 'guardar_prospecto_inteletgroup.php', $prospectData, $cookieFile);
echo "   Código HTTP: {$prospectResult['httpCode']}\n";
echo "   Respuesta: {$prospectResult['response']}\n";

$prospectJson = json_decode($prospectResult['response'], true);
if ($prospectJson) {
    if ($prospectJson['success']) {
        $prospecto_id = $prospectJson['prospecto_id'];
        echo "   ✅ PRIMER PROSPECTO CREADO EXITOSAMENTE - ID: $prospecto_id\n";
        
        // Paso 3: Intentar crear el mismo prospecto inmediatamente (debería fallar)
        echo "\n3. Intentando crear el mismo prospecto inmediatamente (debería fallar)...\n";
        
        $duplicateResult = makeCurlRequest($baseUrl . 'guardar_prospecto_inteletgroup.php', $prospectData, $cookieFile);
        echo "   Código HTTP: {$duplicateResult['httpCode']}\n";
        echo "   Respuesta: {$duplicateResult['response']}\n";
        
        $duplicateJson = json_decode($duplicateResult['response'], true);
        if ($duplicateJson) {
            if (!$duplicateJson['success'] && isset($duplicateJson['duplicate']) && $duplicateJson['duplicate']) {
                echo "   ✅ DUPLICADO DETECTADO CORRECTAMENTE\n";
                echo "   ✅ Mensaje: {$duplicateJson['message']}\n";
            } else {
                echo "   ❌ ERROR: El sistema no detectó el duplicado correctamente\n";
            }
        } else {
            echo "   ❌ ERROR: Respuesta no es JSON válido\n";
        }
        
        // Paso 4: Esperar 6 minutos y probar nuevamente (simulado con cambio de RUT)
        echo "\n4. Probando con RUT diferente (simula esperar 6 minutos)...\n";
        $newRut = rand(10000000, 99999999) . '-' . rand(0, 9);
        $prospectData['rut_cliente'] = $newRut;
        echo "   Nuevo RUT: $newRut\n";
        
        $newResult = makeCurlRequest($baseUrl . 'guardar_prospecto_inteletgroup.php', $prospectData, $cookieFile);
        echo "   Código HTTP: {$newResult['httpCode']}\n";
        echo "   Respuesta: {$newResult['response']}\n";
        
        $newJson = json_decode($newResult['response'], true);
        if ($newJson && $newJson['success']) {
            echo "   ✅ SEGUNDO PROSPECTO CREADO EXITOSAMENTE - ID: {$newJson['prospecto_id']}\n";
        } else {
            echo "   ❌ ERROR: No se pudo crear el segundo prospecto\n";
        }
        
    } else {
        echo "   ❌ ERROR CREANDO PRIMER PROSPECTO: {$prospectJson['message']}\n";
        
        // Verificar si es un error de duplicado inesperado
        if (isset($prospectJson['duplicate']) && $prospectJson['duplicate']) {
            echo "   🔍 PROBLEMA DETECTADO: El sistema reporta duplicado cuando no debería\n";
            echo "   🔍 Este es exactamente el problema que estamos solucionando\n";
        }
    }
} else {
    echo "   ❌ ERROR: Respuesta no es JSON válido\n";
    echo "   Respuesta cruda: {$prospectResult['response']}\n";
}

// Limpiar
if (file_exists($cookieFile)) {
    unlink($cookieFile);
}

echo "\n=== FIN PRUEBA CORRECCIÓN DUPLICADOS ===\n";
?>
