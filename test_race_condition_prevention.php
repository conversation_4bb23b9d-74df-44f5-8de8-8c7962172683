<?php
// Script para probar la prevención de race conditions
echo "=== PRUEBA PREVENCIÓN RACE CONDITIONS ===\n";

// Configuración
$baseUrl = 'https://www.gestarservicios.cl/intranet/dist/';
$username = '11111111-1';
$password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';

// Función para hacer solicitudes cURL
function makeCurlRequest($url, $postData = null, $cookieFile = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    if ($cookieFile) {
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
    }
    
    if ($postData !== null) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json',
            'X-Requested-With: XMLHttpRequest'
        ]);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'response' => $response,
        'httpCode' => $httpCode,
        'error' => $error
    ];
}

// Función para hacer solicitud simultánea
function makeSimultaneousRequest($requestId, $prospectData, $cookieFile) {
    $startTime = microtime(true);
    echo "   [REQUEST $requestId] Iniciando a las " . date('H:i:s.') . substr(microtime(), 2, 3) . "\n";
    
    $result = makeCurlRequest('https://www.gestarservicios.cl/intranet/dist/guardar_prospecto_inteletgroup.php', $prospectData, $cookieFile);
    
    $endTime = microtime(true);
    $duration = round(($endTime - $startTime) * 1000, 2);
    
    echo "   [REQUEST $requestId] Completado en {$duration}ms - HTTP {$result['httpCode']}\n";
    
    $json = json_decode($result['response'], true);
    if ($json) {
        if ($json['success']) {
            echo "   [REQUEST $requestId] ✅ ÉXITO - ID: {$json['prospecto_id']}\n";
        } else {
            echo "   [REQUEST $requestId] ❌ ERROR - {$json['message']}\n";
        }
    } else {
        echo "   [REQUEST $requestId] ❌ RESPUESTA INVÁLIDA\n";
    }
    
    return $result;
}

// Paso 1: Login
echo "1. Realizando login...\n";
$cookieFile = tempnam(sys_get_temp_dir(), 'cookies');

$loginData = [
    'rut' => $username,
    'clave' => $password
];

$loginResult = makeCurlRequest($baseUrl . 'ControllerGestar.php', $loginData, $cookieFile);
if ($loginResult['httpCode'] === 200) {
    $loginJson = json_decode($loginResult['response'], true);
    if ($loginJson && isset($loginJson['success']) && $loginJson['success']) {
        echo "   ✅ Login exitoso\n";
    } else {
        echo "   ❌ Login fallido\n";
        exit(1);
    }
} else {
    echo "   ❌ Error en login\n";
    exit(1);
}

// Paso 2: Preparar datos de prospecto único
echo "\n2. Preparando datos de prospecto...\n";
$uniqueRut = rand(10000000, 99999999) . '-' . rand(0, 9);

$prospectData = [
    'usuario_id' => '4',
    'rut_cliente' => $uniqueRut,
    'razon_social' => 'EMPRESA PRUEBA RACE CONDITION LTDA',
    'nombre_ejecutivo' => 'Ejecutivo Race Test',
    'rubro' => 'Servicios de prueba race condition',
    'direccion_comercial' => 'Dirección Race 123',
    'telefono_celular' => '*********',
    'email' => '<EMAIL>',
    'tipo_persona' => 'Jurídica',
    'numero_pos' => '54321',
    'tipo_cuenta' => 'Corriente',
    'numero_cuenta_bancaria' => '*********',
    'dias_atencion' => 'Lunes a Viernes',
    'horario_atencion' => '8:00 - 17:00',
    'contrata_boleta' => 'Factura',
    'competencia_actual' => 'Transbank'
];

echo "   RUT generado: $uniqueRut\n";

// Paso 3: Simular solicitudes simultáneas usando procesos paralelos
echo "\n3. Simulando solicitudes simultáneas...\n";

// Crear múltiples archivos temporales con los datos
$tempFiles = [];
for ($i = 1; $i <= 3; $i++) {
    $tempFile = tempnam(sys_get_temp_dir(), "race_test_$i");
    $tempFiles[] = $tempFile;
    
    $scriptContent = "<?php
\$prospectData = " . var_export($prospectData, true) . ";
\$cookieFile = '$cookieFile';

function makeCurlRequest(\$url, \$postData = null, \$cookieFile = null) {
    \$ch = curl_init();
    curl_setopt(\$ch, CURLOPT_URL, \$url);
    curl_setopt(\$ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt(\$ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt(\$ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt(\$ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt(\$ch, CURLOPT_TIMEOUT, 30);
    curl_setopt(\$ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    if (\$cookieFile) {
        curl_setopt(\$ch, CURLOPT_COOKIEJAR, \$cookieFile);
        curl_setopt(\$ch, CURLOPT_COOKIEFILE, \$cookieFile);
    }
    
    if (\$postData !== null) {
        curl_setopt(\$ch, CURLOPT_POST, true);
        curl_setopt(\$ch, CURLOPT_POSTFIELDS, http_build_query(\$postData));
        curl_setopt(\$ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json',
            'X-Requested-With: XMLHttpRequest'
        ]);
    }
    
    \$response = curl_exec(\$ch);
    \$httpCode = curl_getinfo(\$ch, CURLINFO_HTTP_CODE);
    \$error = curl_error(\$ch);
    curl_close(\$ch);
    
    return [
        'response' => \$response,
        'httpCode' => \$httpCode,
        'error' => \$error
    ];
}

\$startTime = microtime(true);
echo 'REQUEST_$i:START:' . \$startTime . \"\\n\";

\$result = makeCurlRequest('https://www.gestarservicios.cl/intranet/dist/guardar_prospecto_inteletgroup.php', \$prospectData, \$cookieFile);

\$endTime = microtime(true);
echo 'REQUEST_$i:END:' . \$endTime . \"\\n\";
echo 'REQUEST_$i:HTTP:' . \$result['httpCode'] . \"\\n\";
echo 'REQUEST_$i:RESPONSE:' . \$result['response'] . \"\\n\";
?>";
    
    file_put_contents($tempFile, $scriptContent);
}

// Ejecutar los scripts en paralelo
$processes = [];
foreach ($tempFiles as $i => $tempFile) {
    $cmd = "C:\\xampp\\php\\php.exe \"$tempFile\"";
    $process = popen($cmd, 'r');
    $processes[] = $process;
    echo "   Proceso " . ($i + 1) . " iniciado\n";
}

// Recopilar resultados
$results = [];
foreach ($processes as $i => $process) {
    $output = stream_get_contents($process);
    pclose($process);
    $results[] = $output;
    echo "   Proceso " . ($i + 1) . " completado\n";
}

// Analizar resultados
echo "\n4. Analizando resultados...\n";
$successCount = 0;
$duplicateCount = 0;
$errorCount = 0;

foreach ($results as $i => $output) {
    echo "   === RESULTADO PROCESO " . ($i + 1) . " ===\n";
    
    if (preg_match('/REQUEST_\d+:RESPONSE:(.+)/', $output, $matches)) {
        $response = $matches[1];
        $json = json_decode($response, true);
        
        if ($json) {
            if ($json['success']) {
                $successCount++;
                echo "   ✅ ÉXITO - ID: {$json['prospecto_id']}\n";
            } else {
                if (isset($json['duplicate']) && $json['duplicate']) {
                    $duplicateCount++;
                    echo "   🔄 DUPLICADO DETECTADO - {$json['message']}\n";
                } else {
                    $errorCount++;
                    echo "   ❌ ERROR - {$json['message']}\n";
                }
            }
        } else {
            $errorCount++;
            echo "   ❌ RESPUESTA INVÁLIDA\n";
        }
    }
}

// Limpiar archivos temporales
foreach ($tempFiles as $tempFile) {
    unlink($tempFile);
}

if (file_exists($cookieFile)) {
    unlink($cookieFile);
}

echo "\n=== RESUMEN FINAL ===\n";
echo "✅ Éxitos: $successCount\n";
echo "🔄 Duplicados detectados: $duplicateCount\n";
echo "❌ Errores: $errorCount\n";

if ($successCount === 1 && $duplicateCount >= 2) {
    echo "\n🎉 ¡PRUEBA EXITOSA! Solo se creó 1 registro, los demás fueron detectados como duplicados.\n";
} else if ($successCount > 1) {
    echo "\n🚨 ¡PROBLEMA! Se crearon múltiples registros ($successCount). El race condition no fue prevenido.\n";
} else {
    echo "\n⚠️ Resultado inesperado. Revisar logs para más detalles.\n";
}

echo "\n=== FIN PRUEBA RACE CONDITIONS ===\n";
?>
