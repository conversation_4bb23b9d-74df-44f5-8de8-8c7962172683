<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Anti-Caché</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        button { padding: 10px 20px; margin: 5px; }
        #results { background: #f5f5f5; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🚫 Test Anti-Caché - InteletGroup</h1>
    
    <div class="test-section">
        <h2>Estado del Service Worker</h2>
        <div id="sw-status"></div>
        <button onclick="checkServiceWorker()">Verificar Service Worker</button>
        <button onclick="clearAllCaches()">Limpiar Todos los Cachés</button>
        <button onclick="unregisterSW()">Desregistrar Service Worker</button>
    </div>
    
    <div class="test-section">
        <h2>Estado del Cache Storage</h2>
        <div id="cache-status"></div>
        <button onclick="checkCacheStorage()">Verificar Cache Storage</button>
    </div>
    
    <div class="test-section">
        <h2>Test de Fetch Sin Caché</h2>
        <div id="fetch-status"></div>
        <button onclick="testFetch()">Test Fetch form_inteletgroup.php</button>
    </div>
    
    <div id="results"></div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            results.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            console.log(message);
        }

        async function checkServiceWorker() {
            const statusDiv = document.getElementById('sw-status');
            
            if ('serviceWorker' in navigator) {
                try {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    statusDiv.innerHTML = `<div class="warning">Service Workers encontrados: ${registrations.length}</div>`;
                    
                    registrations.forEach((reg, index) => {
                        statusDiv.innerHTML += `<div>SW ${index + 1}: ${reg.scope}</div>`;
                        statusDiv.innerHTML += `<div>Estado: ${reg.active ? 'Activo' : 'Inactivo'}</div>`;
                    });
                    
                    log(`Service Workers encontrados: ${registrations.length}`, 'warning');
                } catch (error) {
                    statusDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
                    log(`Error verificando Service Worker: ${error.message}`, 'error');
                }
            } else {
                statusDiv.innerHTML = `<div class="success">Service Worker no soportado</div>`;
                log('Service Worker no soportado', 'success');
            }
        }

        async function checkCacheStorage() {
            const statusDiv = document.getElementById('cache-status');
            
            if ('caches' in window) {
                try {
                    const cacheNames = await caches.keys();
                    statusDiv.innerHTML = `<div class="${cacheNames.length > 0 ? 'warning' : 'success'}">Cachés encontrados: ${cacheNames.length}</div>`;
                    
                    cacheNames.forEach(name => {
                        statusDiv.innerHTML += `<div>Caché: ${name}</div>`;
                    });
                    
                    log(`Cachés en Cache Storage: ${cacheNames.length}`, cacheNames.length > 0 ? 'warning' : 'success');
                } catch (error) {
                    statusDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
                    log(`Error verificando Cache Storage: ${error.message}`, 'error');
                }
            } else {
                statusDiv.innerHTML = `<div class="success">Cache Storage no soportado</div>`;
                log('Cache Storage no soportado', 'success');
            }
        }

        async function clearAllCaches() {
            log('🚫 Iniciando limpieza total de cachés...', 'warning');
            
            try {
                // Limpiar Cache Storage
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(cacheNames.map(name => caches.delete(name)));
                    log(`✅ ${cacheNames.length} cachés eliminados del Cache Storage`, 'success');
                }
                
                // Enviar comando al Service Worker
                if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
                    const messageChannel = new MessageChannel();
                    messageChannel.port1.onmessage = function(event) {
                        if (event.data.success) {
                            log('✅ Service Worker: Cachés eliminados', 'success');
                        }
                    };
                    navigator.serviceWorker.controller.postMessage({
                        type: 'CLEAR_CACHE'
                    }, [messageChannel.port2]);
                }
                
                // Actualizar estados
                setTimeout(() => {
                    checkServiceWorker();
                    checkCacheStorage();
                }, 1000);
                
            } catch (error) {
                log(`❌ Error limpiando cachés: ${error.message}`, 'error');
            }
        }

        async function unregisterSW() {
            if ('serviceWorker' in navigator) {
                try {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    for (let registration of registrations) {
                        await registration.unregister();
                        log(`✅ Service Worker desregistrado: ${registration.scope}`, 'success');
                    }
                    
                    setTimeout(() => {
                        checkServiceWorker();
                        checkCacheStorage();
                    }, 1000);
                    
                } catch (error) {
                    log(`❌ Error desregistrando Service Worker: ${error.message}`, 'error');
                }
            }
        }

        async function testFetch() {
            const statusDiv = document.getElementById('fetch-status');
            const url = 'form_inteletgroup.php';
            
            try {
                log(`🔄 Haciendo fetch a ${url}...`, 'info');
                
                const response = await fetch(url, {
                    method: 'GET',
                    cache: 'no-store',
                    headers: {
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });
                
                const timestamp = new Date().toLocaleTimeString();
                statusDiv.innerHTML = `
                    <div class="success">✅ Fetch exitoso</div>
                    <div>Status: ${response.status}</div>
                    <div>Timestamp: ${timestamp}</div>
                    <div>Headers Cache-Control: ${response.headers.get('Cache-Control') || 'No definido'}</div>
                `;
                
                log(`✅ Fetch exitoso a ${url} - Status: ${response.status}`, 'success');
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                log(`❌ Error en fetch: ${error.message}`, 'error');
            }
        }

        // Verificar estado inicial
        window.addEventListener('load', () => {
            log('🚀 Página cargada - Verificando estado inicial...', 'info');
            checkServiceWorker();
            checkCacheStorage();
        });
    </script>
</body>
</html>
