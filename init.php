<?php
/**
 * Archivo de inicialización global para todas las páginas
 *
 * Se encarga de:
 * - Cargar el sistema de configuración multi-entorno
 * - Aplicar headers apropiados según el entorno
 * - Cargar utilidades y helpers
 * - Configurar el entorno PHP
 *
 * <AUTHOR> Intranet Gestar
 * @version 2.0
 */

// Definir constante de ruta absoluta
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

// Incluir sistema de configuración multi-entorno
require_once ABSPATH . 'config.php';

// Incluir helpers de URL
require_once ABSPATH . 'url_helpers.php';

// Obtener instancia de configuración
$envConfig = EnvironmentConfig::getInstance();

// Aplicar headers ULTRA AGRESIVOS para eliminar TODO el caché
if (!headers_sent()) {
    // Invalidar OPcache SIEMPRE
    if (function_exists('opcache_invalidate') && ini_get('opcache.enable')) {
        opcache_invalidate($_SERVER['SCRIPT_FILENAME'], true);
    }

    // Reset completo de OPcache
    if (function_exists('opcache_reset')) {
        opcache_reset();
    }

    // Headers ultra agresivos para ELIMINAR TODO el caché (desarrollo Y producción)
    header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0, s-maxage=0, proxy-revalidate, no-transform, private");
    header("Cache-Control: post-check=0, pre-check=0", false);
    header("Pragma: no-cache");
    header('Expires: 0');
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
    header('Vary: *');
    header('X-Accel-Expires: 0');
    header('X-Cache-Enabled: False');
    header('Surrogate-Control: no-store');
    header('Edge-Control: no-store');
    header('CF-Cache-Status: BYPASS');
    header('ETag: "' . md5(microtime(true) . rand()) . '"');

    // Headers de seguridad
    header('X-Frame-Options: SAMEORIGIN');
    header('X-Content-Type-Options: nosniff');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');

    // HTTPS en producción
    if (!$envConfig->isDevelopment() && $envConfig->get('ssl_required')) {
        header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
    }

    // Headers comunes
    header('Content-Type: text/html; charset=UTF-8');

    // Header personalizado para debugging
    header('X-No-Cache-Init: ' . date('Y-m-d H:i:s') . ' - ' . ($envConfig->isDevelopment() ? 'DEV' : 'PROD'));
}

// Cargar utilidades de caché si existe
if (file_exists(ABSPATH . 'cache_utils.php')) {
    require_once ABSPATH . 'cache_utils.php';
}

// Log de inicialización en desarrollo
if ($envConfig->isDevelopment()) {
    error_log("[INIT] Sistema inicializado - Entorno: " . $envConfig->getEnvironment() . " - URL Base: " . $envConfig->getBaseUrl());
}
