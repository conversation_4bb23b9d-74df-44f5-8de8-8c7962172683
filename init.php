<?php
/**
 * Archivo de inicialización global para todas las páginas
 *
 * Se encarga de:
 * - Cargar el sistema de configuración multi-entorno
 * - Aplicar headers apropiados según el entorno
 * - Cargar utilidades y helpers
 * - Configurar el entorno PHP
 *
 * <AUTHOR> Intranet Gestar
 * @version 2.0
 */

// Definir constante de ruta absoluta
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

// Incluir sistema de configuración multi-entorno
require_once ABSPATH . 'config.php';

// Incluir helpers de URL
require_once ABSPATH . 'url_helpers.php';

// Obtener instancia de configuración
$envConfig = EnvironmentConfig::getInstance();

// Aplicar headers apropiados según el entorno
if (!headers_sent()) {
    if ($envConfig->isDevelopment()) {
        // Headers para desarrollo - sin cache
        if (function_exists('opcache_invalidate') && ini_get('opcache.enable')) {
            opcache_invalidate($_SERVER['SCRIPT_FILENAME'], true);
        }

        header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
        header("Cache-Control: post-check=0, pre-check=0", false);
        header("Pragma: no-cache");
        header('Expires: 0');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');

        // Headers de seguridad para desarrollo
        header('X-Frame-Options: SAMEORIGIN');
        header('X-Content-Type-Options: nosniff');
        header('X-XSS-Protection: 1; mode=block');
    } else {
        // Headers para producción - con cache controlado
        header('X-Frame-Options: DENY');
        header('X-Content-Type-Options: nosniff');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');

        // Solo HTTPS en producción
        if ($envConfig->get('ssl_required')) {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
        }
    }

    // Headers comunes
    header('Content-Type: text/html; charset=UTF-8');
}

// Cargar utilidades de caché si existe
if (file_exists(ABSPATH . 'cache_utils.php')) {
    require_once ABSPATH . 'cache_utils.php';
}

// Log de inicialización en desarrollo
if ($envConfig->isDevelopment()) {
    error_log("[INIT] Sistema inicializado - Entorno: " . $envConfig->getEnvironment() . " - URL Base: " . $envConfig->getBaseUrl());
}
