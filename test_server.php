<?php
// Archivo de prueba para verificar que el servidor funciona
echo "Servidor funcionando correctamente<br>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Fecha/Hora: " . date('Y-m-d H:i:s') . "<br>";

// Probar el nuevo sistema de configuración
try {
    require_once 'init.php';
    echo "<h3>Sistema de Configuración Multi-Entorno</h3>";
    
    $config = EnvironmentConfig::getInstance();
    echo "Entorno detectado: " . $config->getEnvironment() . "<br>";
    echo "URL Base: " . $config->getBaseUrl() . "<br>";
    echo "Debug habilitado: " . ($config->get('debug') ? 'Sí' : 'No') . "<br>";
    
    echo "<h3>Pruebas de URLs</h3>";
    echo "base_url(): " . base_url() . "<br>";
    echo "base_url('login.php'): " . base_url('login.php') . "<br>";
    echo "css_url('bootstrap.min.css'): " . css_url('bootstrap.min.css') . "<br>";
    
} catch (Exception $e) {
    echo "<h3>Error en el sistema de configuración:</h3>";
    echo $e->getMessage() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
