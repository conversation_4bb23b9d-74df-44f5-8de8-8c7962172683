<?php
// Script de prueba para verificar guardar_prospecto_inteletgroup.php

echo "<h2>Test de guardar_prospecto_inteletgroup.php con POST simulado</h2>";

// Simular datos POST
$_SERVER['REQUEST_METHOD'] = 'POST';
$_POST = [
    'usuario_id' => '1',
    'nombre_ejecutivo' => 'TEST EJECUTIVO',
    'rut_cliente' => '********-1',
    'razon_social' => 'EMPRESA TEST POST',
    'rubro' => 'COMERCIO',
    'direccion_comercial' => 'DIRECCION TEST 123',
    'telefono_celular' => '*********',
    'email' => '<EMAIL>',
    'tipo_persona' => 'Natural',
    'numero_pos' => 'POS123',
    'tipo_cuenta' => 'Cuenta Vista',
    'numero_cuenta_bancaria' => '********',
    'dias_atencion' => 'Lunes a Viernes',
    'horario_atencion' => '09:00 - 18:00',
    'contrata_boleta' => 'Si',
    'competencia_actual' => 'Transbank'
];

// Simular archivos vacíos
$_FILES = [];

// Capturar la salida
ob_start();

// Incluir el archivo a probar
try {
    require_once 'guardar_prospecto_inteletgroup.php';
} catch (Exception $e) {
    echo "Error al incluir archivo: " . $e->getMessage();
}

$output = ob_get_contents();
ob_end_clean();

// Mostrar resultado
echo "<h3>Respuesta del servidor:</h3>";
echo "<pre>";
echo htmlspecialchars($output);
echo "</pre>";

// Intentar decodificar JSON
echo "<h3>Análisis de la respuesta:</h3>";
$json = json_decode($output, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "<p style='color: green;'>✓ Respuesta JSON válida</p>";
    echo "<pre>";
    print_r($json);
    echo "</pre>";
} else {
    echo "<p style='color: red;'>✗ Error al decodificar JSON: " . json_last_error_msg() . "</p>";
}

// Verificar headers
echo "<h3>Headers enviados:</h3>";
$headers = headers_list();
echo "<pre>";
print_r($headers);
echo "</pre>";
?>