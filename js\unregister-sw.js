// ========================================================
// DESREGISTRAR SERVICE WORKERS - EVITAR BUCLES INFINITOS
// ========================================================

console.log('🔄 Iniciando desregistro de Service Workers...');

// Función para desregistrar todos los Service Workers
function unregisterAllServiceWorkers() {
    if ('serviceWorker' in navigator) {
        console.log('🔍 Buscando Service Workers registrados...');
        
        navigator.serviceWorker.getRegistrations().then(function(registrations) {
            if (registrations.length === 0) {
                console.log('✅ No hay Service Workers registrados');
                return;
            }
            
            console.log(`🚫 Encontrados ${registrations.length} Service Workers. Desregistrando...`);
            
            registrations.forEach(function(registration, index) {
                console.log(`🚫 Desregistrando Service Worker ${index + 1}:`, registration.scope);
                
                registration.unregister().then(function(success) {
                    if (success) {
                        console.log(`✅ Service Worker ${index + 1} desregistrado exitosamente`);
                    } else {
                        console.warn(`⚠️ No se pudo desregistrar Service Worker ${index + 1}`);
                    }
                }).catch(function(error) {
                    console.error(`❌ Error al desregistrar Service Worker ${index + 1}:`, error);
                });
            });
            
            // Limpiar cachés después de desregistrar
            setTimeout(function() {
                clearAllCaches();
            }, 1000);
            
        }).catch(function(error) {
            console.error('❌ Error al obtener registros de Service Worker:', error);
        });
    } else {
        console.log('ℹ️ Service Workers no soportados en este navegador');
    }
}

// Función para limpiar todos los cachés
function clearAllCaches() {
    if ('caches' in window) {
        console.log('🗑️ Limpiando todos los cachés...');
        
        caches.keys().then(function(cacheNames) {
            if (cacheNames.length === 0) {
                console.log('✅ No hay cachés para limpiar');
                return;
            }
            
            console.log(`🗑️ Encontrados ${cacheNames.length} cachés. Eliminando...`);
            
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    console.log('🗑️ Eliminando caché:', cacheName);
                    return caches.delete(cacheName);
                })
            );
        }).then(function() {
            console.log('✅ Todos los cachés eliminados');
        }).catch(function(error) {
            console.error('❌ Error al limpiar cachés:', error);
        });
    } else {
        console.log('ℹ️ Cache API no soportada en este navegador');
    }
}

// Función para prevenir que se registren nuevos Service Workers
function preventServiceWorkerRegistration() {
    if ('serviceWorker' in navigator) {
        // Sobrescribir el método register para evitar nuevos registros
        const originalRegister = navigator.serviceWorker.register;
        navigator.serviceWorker.register = function() {
            console.warn('🚫 Registro de Service Worker bloqueado para evitar bucles');
            return Promise.reject(new Error('Service Worker registration blocked'));
        };
        
        console.log('🛡️ Registro de nuevos Service Workers bloqueado');
    }
}

// Ejecutar inmediatamente
unregisterAllServiceWorkers();
preventServiceWorkerRegistration();

// Ejecutar también cuando el DOM esté listo
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
            unregisterAllServiceWorkers();
        }, 500);
    });
} else {
    // DOM ya está listo
    setTimeout(function() {
        unregisterAllServiceWorkers();
    }, 500);
}

// Ejecutar periódicamente para asegurar que no se registren nuevos SW
setInterval(function() {
    unregisterAllServiceWorkers();
}, 30000); // Cada 30 segundos

console.log('✅ Script de desregistro de Service Workers cargado');
