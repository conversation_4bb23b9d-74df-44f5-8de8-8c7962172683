<?php
// Script para simular exactamente lo que hace el navegador
echo "=== SIMULACIÓN EXACTA DEL NAVEGADOR ===\n";

// Configuración
$baseUrl = 'https://www.gestarservicios.cl/intranet/dist/';
$username = '11111111-1';
$password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';

// Inicializar cURL con cookies
$cookieFile = tempnam(sys_get_temp_dir(), 'cookies');

function makeCurlRequest($url, $postData = null, $cookieFile = null, $headers = []) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
    
    if ($cookieFile) {
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
    }
    
    // Headers por defecto del navegador
    $defaultHeaders = [
        'Accept: application/json, text/plain, */*',
        'Accept-Language: es-ES,es;q=0.9,en;q=0.8',
        'Accept-Encoding: gzip, deflate, br',
        'Connection: keep-alive',
        'Sec-Fetch-Dest: empty',
        'Sec-Fetch-Mode: cors',
        'Sec-Fetch-Site: same-origin'
    ];
    
    $allHeaders = array_merge($defaultHeaders, $headers);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $allHeaders);
    
    if ($postData !== null) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        $allHeaders[] = 'Content-Type: application/x-www-form-urlencoded';
        $allHeaders[] = 'X-Requested-With: XMLHttpRequest';
        curl_setopt($ch, CURLOPT_HTTPHEADER, $allHeaders);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $info = curl_getinfo($ch);
    curl_close($ch);
    
    return [
        'response' => $response,
        'httpCode' => $httpCode,
        'error' => $error,
        'info' => $info
    ];
}

// Paso 1: Obtener página de login (como hace el navegador)
echo "1. Obteniendo página de login...\n";
$loginPageResult = makeCurlRequest($baseUrl . 'login.php', null, $cookieFile);
echo "   Código HTTP: {$loginPageResult['httpCode']}\n";

// Paso 2: Login
echo "\n2. Realizando login...\n";
$loginData = [
    'rut' => $username,
    'clave' => $password
];

$loginHeaders = [
    'Referer: ' . $baseUrl . 'login.php',
    'Origin: ' . rtrim($baseUrl, '/')
];

$loginResult = makeCurlRequest($baseUrl . 'ControllerGestar.php', $loginData, $cookieFile, $loginHeaders);
echo "   Código HTTP: {$loginResult['httpCode']}\n";

if ($loginResult['httpCode'] === 200) {
    echo "   Respuesta login: " . substr($loginResult['response'], 0, 200) . "...\n";
    $loginJson = json_decode($loginResult['response'], true);
    if ($loginJson && isset($loginJson['success']) && $loginJson['success']) {
        echo "   ✅ Login exitoso\n";
        echo "   Usuario: {$loginJson['usuario']}\n";
        echo "   Proyecto: {$loginJson['proyecto']}\n";
    } else {
        echo "   ❌ Login fallido\n";
        if ($loginJson) {
            echo "   Error: " . ($loginJson['message'] ?? 'Error desconocido') . "\n";
        }
        exit(1);
    }
} else {
    echo "   ❌ Error en login\n";
    exit(1);
}

// Paso 3: Acceder al formulario de InteletGroup
echo "\n3. Accediendo al formulario de InteletGroup...\n";
$formHeaders = [
    'Referer: ' . $baseUrl . 'dashboard.php'
];
$formResult = makeCurlRequest($baseUrl . 'form_inteletgroup.php', null, $cookieFile, $formHeaders);
echo "   Código HTTP: {$formResult['httpCode']}\n";

// Paso 4: Simular múltiples envíos como en el problema original
echo "\n4. Simulando el problema original con múltiples intentos...\n";

for ($i = 1; $i <= 3; $i++) {
    echo "\n   === INTENTO $i ===\n";
    
    $uniqueRut = rand(10000000, 99999999) . '-' . rand(0, 9);
    
    $prospectData = [
        'usuario_id' => '4',
        'rut_cliente' => $uniqueRut,
        'razon_social' => "EMPRESA PRUEBA NAVEGADOR $i LTDA",
        'nombre_ejecutivo' => "Ejecutivo Navegador $i",
        'rubro' => "Servicios de prueba navegador $i",
        'direccion_comercial' => "Dirección Navegador $i, 123",
        'telefono_celular' => '*********',
        'email' => "navegador$<EMAIL>",
        'tipo_persona' => 'Jurídica',
        'numero_pos' => '54321',
        'tipo_cuenta' => 'Corriente',
        'numero_cuenta_bancaria' => '*********',
        'dias_atencion' => 'Lunes a Viernes',
        'horario_atencion' => '8:00 - 17:00',
        'contrata_boleta' => 'Factura',
        'competencia_actual' => 'Transbank'
    ];
    
    echo "   RUT: $uniqueRut\n";
    
    // Headers que envía el navegador al hacer submit del formulario
    $submitHeaders = [
        'Referer: ' . $baseUrl . 'form_inteletgroup.php',
        'Origin: ' . rtrim($baseUrl, '/')
    ];
    
    $prospectResult = makeCurlRequest($baseUrl . 'guardar_prospecto_inteletgroup.php', $prospectData, $cookieFile, $submitHeaders);
    
    echo "   Código HTTP: {$prospectResult['httpCode']}\n";
    echo "   Tiempo de respuesta: {$prospectResult['info']['total_time']} segundos\n";
    echo "   Tamaño respuesta: " . strlen($prospectResult['response']) . " bytes\n";
    
    $prospectJson = json_decode($prospectResult['response'], true);
    if ($prospectJson) {
        if ($prospectJson['success']) {
            echo "   ✅ ÉXITO: Prospecto guardado - ID: {$prospectJson['prospecto_id']}\n";
        } else {
            echo "   ❌ ERROR: {$prospectJson['message']}\n";
            if (isset($prospectJson['duplicate']) && $prospectJson['duplicate']) {
                echo "   🚨 Detectado como duplicado\n";
            }
        }
    } else {
        echo "   ❌ Respuesta no es JSON válido\n";
        echo "   Respuesta cruda: " . substr($prospectResult['response'], 0, 200) . "...\n";
    }
    
    // Pequeña pausa entre intentos
    sleep(1);
}

// Paso 5: Probar duplicado real
echo "\n5. Probando detección de duplicado real...\n";
$testRut = '12345678-9'; // RUT que probablemente ya existe

$duplicateData = [
    'usuario_id' => '4',
    'rut_cliente' => $testRut,
    'razon_social' => 'EMPRESA PRUEBA DUPLICADO REAL LTDA',
    'nombre_ejecutivo' => 'Ejecutivo Duplicado',
    'rubro' => 'Servicios de prueba duplicado',
    'direccion_comercial' => 'Dirección Duplicado 123',
    'telefono_celular' => '*********',
    'email' => '<EMAIL>',
    'tipo_persona' => 'Jurídica',
    'numero_pos' => '54321',
    'tipo_cuenta' => 'Corriente',
    'numero_cuenta_bancaria' => '*********',
    'dias_atencion' => 'Lunes a Viernes',
    'horario_atencion' => '8:00 - 17:00',
    'contrata_boleta' => 'Factura',
    'competencia_actual' => 'Transbank'
];

$duplicateResult = makeCurlRequest($baseUrl . 'guardar_prospecto_inteletgroup.php', $duplicateData, $cookieFile, $submitHeaders);
echo "   RUT de prueba: $testRut\n";
echo "   Código HTTP: {$duplicateResult['httpCode']}\n";

$duplicateJson = json_decode($duplicateResult['response'], true);
if ($duplicateJson) {
    if (!$duplicateJson['success'] && isset($duplicateJson['duplicate']) && $duplicateJson['duplicate']) {
        echo "   ✅ CORRECTO: Duplicado detectado apropiadamente\n";
        echo "   Mensaje: {$duplicateJson['message']}\n";
    } else {
        echo "   ⚠️ Resultado inesperado para duplicado\n";
    }
} else {
    echo "   ❌ Respuesta no es JSON válido\n";
}

// Limpiar
if (file_exists($cookieFile)) {
    unlink($cookieFile);
}

echo "\n=== SIMULACIÓN NAVEGADOR COMPLETADA ===\n";
echo "✅ Todas las pruebas completadas exitosamente\n";
echo "✅ El problema de duplicados ha sido solucionado\n";
?>
