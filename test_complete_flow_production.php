<?php
// Script para probar el flujo completo en producción: login + crear prospecto + documentos
echo "=== PRUEBA COMPLETA FLUJO PRODUCCIÓN ===\n";

// Configuración
$baseUrl = 'https://www.gestarservicios.cl/intranet/dist/';
$username = '11111111-1';
$password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';

// Inicializar cURL con cookies
$cookieFile = tempnam(sys_get_temp_dir(), 'cookies');
echo "Archivo de cookies: $cookieFile\n\n";

// Función para hacer solicitudes cURL
function makeCurlRequest($url, $postData = null, $files = null, $cookieFile = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    if ($cookieFile) {
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
    }
    
    if ($postData !== null) {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($files) {
            // Para archivos, usar multipart/form-data
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        } else {
            // Para datos normales
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/x-www-form-urlencoded',
                'Accept: application/json',
                'X-Requested-With: XMLHttpRequest'
            ]);
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'response' => $response,
        'httpCode' => $httpCode,
        'error' => $error
    ];
}

// Paso 1: Obtener la página de login para obtener tokens CSRF si los hay
echo "1. Obteniendo página de login...\n";
$loginPageResult = makeCurlRequest($baseUrl . 'login.php', null, null, $cookieFile);
echo "   Código HTTP: {$loginPageResult['httpCode']}\n";

if ($loginPageResult['httpCode'] !== 200) {
    echo "   ❌ Error obteniendo página de login\n";
    exit(1);
}
echo "   ✅ Página de login obtenida\n\n";

// Paso 2: Hacer login
echo "2. Realizando login...\n";
$loginData = [
    'rut' => $username,
    'clave' => $password
];

$loginResult = makeCurlRequest($baseUrl . 'ControllerGestar.php', $loginData, null, $cookieFile);
echo "   Código HTTP: {$loginResult['httpCode']}\n";

// Verificar si el login fue exitoso
if ($loginResult['httpCode'] === 200) {
    // Intentar decodificar la respuesta JSON
    $loginJson = json_decode($loginResult['response'], true);

    if ($loginJson && isset($loginJson['success'])) {
        if ($loginJson['success']) {
            echo "   ✅ Login exitoso\n";
            echo "   Usuario: {$loginJson['usuario']}\n";
            echo "   Proyecto: {$loginJson['proyecto']}\n";
        } else {
            echo "   ❌ Credenciales incorrectas: {$loginJson['message']}\n";
            exit(1);
        }
    } else {
        echo "   ❌ Respuesta no es JSON válido\n";
        echo "   Respuesta: " . substr($loginResult['response'], 0, 300) . "...\n";
        exit(1);
    }
} else {
    echo "   ❌ Error en login - Código HTTP: {$loginResult['httpCode']}\n";
    exit(1);
}
echo "\n";

// Paso 3: Acceder al formulario de InteletGroup
echo "3. Accediendo al formulario de InteletGroup...\n";
$formResult = makeCurlRequest($baseUrl . 'form_inteletgroup.php', null, null, $cookieFile);
echo "   Código HTTP: {$formResult['httpCode']}\n";

if ($formResult['httpCode'] === 200) {
    echo "   ✅ Formulario accesible\n";
} else {
    echo "   ❌ Error accediendo al formulario\n";
    echo "   Respuesta: " . substr($formResult['response'], 0, 200) . "...\n";
}
echo "\n";

// Paso 4: Crear archivos de prueba para documentos
echo "4. Creando archivos de prueba...\n";
$testFiles = [];
$fileNames = ['documento1.txt', 'documento2.txt', 'documento3.txt'];

foreach ($fileNames as $fileName) {
    $filePath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . $fileName;
    file_put_contents($filePath, "Contenido de prueba para $fileName\nCreado: " . date('Y-m-d H:i:s'));
    $testFiles[] = $filePath;
    echo "   ✅ Creado: $filePath\n";
}
echo "\n";

// Paso 5: Enviar prospecto con documentos
echo "5. Enviando prospecto con documentos...\n";

// Generar RUT único para evitar duplicados
$uniqueRut = rand(10000000, 99999999) . '-' . rand(0, 9);

$prospectData = [
    'usuario_id' => '4',
    'rut_cliente' => $uniqueRut,
    'razon_social' => 'EMPRESA PRUEBA COMPLETA LTDA',
    'nombre_ejecutivo' => 'Ejecutivo Prueba Completa',
    'rubro' => 'Servicios de prueba completa',
    'direccion_comercial' => 'Dirección Prueba Completa 123',
    'telefono_celular' => '*********',
    'email' => '<EMAIL>',
    'tipo_persona' => 'Jurídica',
    'numero_pos' => '54321',
    'tipo_cuenta' => 'Corriente',
    'numero_cuenta_bancaria' => '*********',
    'dias_atencion' => 'Lunes a Viernes',
    'horario_atencion' => '8:00 - 17:00',
    'contrata_boleta' => 'Factura',
    'competencia_actual' => 'Transbank'
];

echo "   RUT generado: $uniqueRut\n";
echo "   Razón Social: {$prospectData['razon_social']}\n";

$prospectResult = makeCurlRequest($baseUrl . 'guardar_prospecto_inteletgroup.php', $prospectData, null, $cookieFile);
echo "   Código HTTP: {$prospectResult['httpCode']}\n";
echo "   Respuesta: {$prospectResult['response']}\n";

// Decodificar respuesta JSON
$jsonResponse = json_decode($prospectResult['response'], true);
if ($jsonResponse) {
    if ($jsonResponse['success']) {
        echo "   ✅ Prospecto creado exitosamente\n";
        echo "   ID del prospecto: {$jsonResponse['prospecto_id']}\n";
    } else {
        echo "   ❌ Error creando prospecto: {$jsonResponse['message']}\n";
    }
} else {
    echo "   ❌ Respuesta no es JSON válido\n";
}

// Limpiar archivos temporales
echo "\n6. Limpiando archivos temporales...\n";
foreach ($testFiles as $filePath) {
    if (file_exists($filePath)) {
        unlink($filePath);
        echo "   ✅ Eliminado: $filePath\n";
    }
}

if (file_exists($cookieFile)) {
    unlink($cookieFile);
    echo "   ✅ Eliminado archivo de cookies\n";
}

echo "\n=== FIN PRUEBA COMPLETA ===\n";
?>
